<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><i class="fas fa-calculator"></i> Check Customer Loan Eligibility</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('admin') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('admin/wacs') ?>">WACS</a></li>
                        <li class="breadcrumb-item active">Check Eligibility</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-search"></i> Eligibility Checker
                            </h3>
                        </div>
                        <div class="card-body">
                            <form id="eligibilityForm" method="post" action="#" novalidate>
                                <?php echo form_hidden($this->security->get_csrf_token_name(), $this->security->get_csrf_hash()); ?>
                                <div class="row">
                                    <div class="col-lg-8 col-md-7">
                                        <div class="form-group">
                                            <label for="ippis_number">
                                                <i class="fas fa-id-card text-primary"></i> IPPIS Number
                                            </label>
                                            <div class="input-group input-group-lg">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                                </div>
                                                <input type="text" class="form-control" id="ippis_number" name="ippis_number"
                                                       required pattern="[0-9]+" placeholder="Enter IPPIS number"
                                                       value="<?php echo isset($_GET['ippis_number']) ? htmlspecialchars($_GET['ippis_number']) : ''; ?>">
                                                <div class="invalid-feedback">Please enter a valid IPPIS number.</div>
                                            </div>
                                            <small class="form-text text-muted">Enter the customer's IPPIS number to check loan eligibility</small>
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-5">
                                        <div class="form-group">
                                            <label class="d-none d-md-block">&nbsp;</label> <!-- Spacer for alignment -->
                                            <button type="submit" class="btn btn-primary btn-lg btn-block">
                                                <i class="fas fa-calculator"></i> Check Eligibility
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>

                            <div id="eligibilityResult" class="mt-4" style="display:none;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<script>
$(document).ready(function() {
    // CSRF token setup
    var csrfName = '<?= $this->security->get_csrf_token_name(); ?>';
    var csrfHash = '<?= $this->security->get_csrf_hash(); ?>';

    // Global variables for caching
    var loanProducts = [];
    var productsMap = {};

    // Utility functions
    function formatCurrency(amount) {
        return parseFloat(amount).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2});
    }

    function resetButton(btn, originalText) {
        btn.prop('disabled', false).text(originalText || 'Check Eligibility');
    }

    function showError(resultDiv, message) {
        resultDiv.html('<div class="alert alert-danger">' + message + '</div>').show();
    }

    function showSuccess(resultDiv, message) {
        resultDiv.html('<div class="alert alert-success">' + message + '</div>').show();
    }

    // Load loan products once and cache them
    function loadLoanProducts() {
        return $.get('<?= base_url('admin/wacs/getLoanProducts') ?>', function(productsRes) {
            if (productsRes.success && Array.isArray(productsRes.data)) {
                loanProducts = productsRes.data.filter(function(p) {
                    return p.status === 'active' || p.status === 1 || p.is_active === 1;
                });
                productsMap = {};
                loanProducts.forEach(function(p) {
                    productsMap[p.id] = p;
                });
            }
        }, 'json');
    }

    // Generate loan product options
    function getLoanProductOptions() {
        return loanProducts.map(function(p) {
            var rate = p.interest_rate ? p.interest_rate + '%' : '';
            var period = p.payback_period ? p.payback_period : (p.tenure ? p.tenure : '');
            var label = `${p.title} - ${rate} - ${period}`;
            return `<option value='${p.id}'>${label}</option>`;
        }).join('');
    }

    // Generate loan application form
    function generateLoanForm(customerId, ippis, eligibilityAmount, successMessage, bvn, accountNumber) {
        var alertHtml = successMessage ? `<div class='alert alert-success'>${successMessage}</div>` : '';
        return `${alertHtml}<form id="loanApplicationForm">
            <input type='hidden' name='customer_id' value='${customerId}'>
            <div class='form-group'>
                <label>IPPIS Number</label>
                <input type='text' class='form-control' value='${ippis}' readonly>
            </div>
            ${bvn ? `<div class='form-group'>
                <label>BVN</label>
                <input type='text' class='form-control' value='${bvn}' readonly>
            </div>` : ''}
            ${accountNumber ? `<div class='form-group'>
                <label>Account Number</label>
                <input type='text' class='form-control' value='${accountNumber}' readonly>
            </div>` : ''}
            <div class='form-group'>
                <label for='loan_product_id'>Loan Product</label>
                <select class='form-control' name='loan_product_id' id='loan_product_id' required>
                    ${getLoanProductOptions()}
                </select>
            </div>
            <div class='form-group'>
                <label for='amount'>Amount</label>
                <div id="amountWarning" class="alert alert-warning" style="display:none;padding:4px 8px;margin-bottom:6px;font-size:90%"></div>
                <input type='number' class='form-control' name='amount' id='amount' required max='${eligibilityAmount}' step='0.01'>
                <div id="repaymentInfo" class="mt-2" style="display:none;"></div>
            </div>
            <button type='submit' class='btn btn-primary'>Apply for Loan</button>
        </form>`;
    }

    // Setup amount validation and repayment calculation
    function setupLoanFormHandlers(eligibilityAmount) {
        // Amount validation
        $(document).off('input.amountWarning').on('input.amountWarning', '#amount', function() {
            var val = parseFloat($(this).val());
            var warningDiv = $('#amountWarning');
            if (eligibilityAmount && val > eligibilityAmount) {
                warningDiv.text('Loan amount is greater than eligible amount of ₦' + formatCurrency(eligibilityAmount)).show();
            } else {
                warningDiv.hide();
            }
        });

        // Repayment calculation
        function updateRepaymentInfo() {
            var productId = $('#loan_product_id').val();
            var amount = parseFloat($('#amount').val());
            var infoDiv = $('#repaymentInfo');

            if (productsMap[productId] && amount > 0) {
                var product = productsMap[productId];
                var rate = parseFloat(product.interest_rate) || 0;
                var period = parseInt(product.payback_period || product.tenure) || 1;
                var totalRepayable = amount + (amount * rate * period / 100);
                var monthlyRepayment = totalRepayable / period;

                infoDiv.html(`
                    <div class='alert alert-info'>
                        <strong>Loan Details:</strong><br>
                        Monthly Repayment: <b>₦${formatCurrency(monthlyRepayment)}</b><br>
                        Tenure: <b>${period} month(s)</b><br>
                        Total Repayable: <b>₦${formatCurrency(totalRepayable)}</b>
                    </div>
                `).show();
            } else {
                infoDiv.hide();
            }
        }

        $(document).off('input.repaymentCalc change.repaymentCalc')
                   .on('input.repaymentCalc change.repaymentCalc', '#amount, #loan_product_id', updateRepaymentInfo);
    }

    // Main eligibility check handler
    $('#eligibilityForm').on('submit', function(e) {
        e.preventDefault();
        var ippis = $('#ippis_number').val().trim();
        var resultDiv = $('#eligibilityResult');
        var btn = $(this).find('button[type="submit"]');

        // Clear previous results
        resultDiv.hide().html('');

        if (!ippis) {
            showError(resultDiv, 'Please enter an IPPIS number.');
            return;
        }

        btn.prop('disabled', true).text('Checking eligibility...');

        // Check eligibility
        $.ajax({
            url: '<?= base_url('admin/wacs/checkEligibility') ?>/' + encodeURIComponent(ippis),
            method: 'GET',
            dataType: 'json',
            success: function(res) {
                // Fix: Check for correct response structure
                var eligibilityAmount = 0;
                var hasEligibility = false;

                if (res && res.success) {
                    // Handle both response formats: direct eligibility or nested in data
                    if (res.eligibility) {
                        eligibilityAmount = parseFloat(res.eligibility) || 0;
                        hasEligibility = true;
                    } else if (res.data && res.data.eligibility) {
                        eligibilityAmount = parseFloat(res.data.eligibility) || 0;
                        hasEligibility = true;
                    }
                }

                if (hasEligibility && eligibilityAmount > 0) {
                    var eligibilityDisplay = `<h3 class='text-success mb-3'>
                        <i class='fas fa-check-circle'></i> Loan Eligibility:
                        <span style='font-weight:bold'>₦${formatCurrency(eligibilityAmount)}</span>
                    </h3>`;

                    // Check customer onboarding status
                    $.ajax({
                        url: '<?= base_url('admin/wacs/ajaxGetCustomerByIppis') ?>',
                        method: 'POST',
                        data: {
                            ippis_number: ippis,
                            [csrfName]: csrfHash
                        },
                        dataType: 'json',
                        success: function(customerRes) {
                            resetButton(btn);
                            var html = eligibilityDisplay;

                            if (customerRes && customerRes.success && customerRes.customer) {
                                // Customer is onboarded - show loan application form
                                var customer = customerRes.customer;
                                html += generateLoanForm(customer.id, ippis, eligibilityAmount, null, customer.bvn, customer.account_number);
                                resultDiv.html(html).show();
                                setupLoanFormHandlers(eligibilityAmount);
                            } else {
                                // Customer not onboarded - show onboarding form
                                html += generateOnboardingForm(ippis, eligibilityAmount);
                                resultDiv.html(html).show();
                                loadBanks();
                            }
                        },
                        error: function() {
                            resetButton(btn);
                            resultDiv.html(eligibilityDisplay + '<div class="alert alert-warning">Could not check customer onboarding status. Please try again.</div>').show();
                        }
                    });
                } else {
                    resetButton(btn);
                    var message = (res && res.message) ? res.message : 'Customer not eligible for loan or eligibility amount is zero.';
                    showError(resultDiv, message);
                }
            },
            error: function(xhr) {
                resetButton(btn);
                var message = 'An error occurred while checking eligibility. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showError(resultDiv, message);
            }
        });
    });
    // Generate onboarding form
    function generateOnboardingForm(ippis, eligibilityAmount) {
        return `<div class='alert alert-info'>
            <i class='fas fa-info-circle'></i> Customer not onboarded. Please complete the onboarding process below:
        </div>
        <form id='onboardCustomerForm'>
            <input type='hidden' name='eligibility' value='${eligibilityAmount}'>
            <input type='hidden' name='ippis_number' value='${ippis}'>
            <input type='hidden' name='${csrfName}' value='${csrfHash}'>

            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>IPPIS Number <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="ippis_number" value="${ippis}" required readonly>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>BVN <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="bvn" required maxlength="11" pattern="[0-9]{11}">
                        <small class="form-text text-muted">11-digit Bank Verification Number</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Account Number <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="account_number" required maxlength="10" pattern="[0-9]{10}">
                        <small class="form-text text-muted">10-digit account number</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Bank <span class="text-danger">*</span></label>
                        <select class="form-control" name="bank" id="bankDropdown" required>
                            <option value="">Select Bank</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>First Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="first_name" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Last Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="last_name" required>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Phone Number <span class="text-danger">*</span></label>
                        <input type="tel" class="form-control" name="phone_number" required pattern="[0-9]{11}">
                        <small class="form-text text-muted">11-digit phone number</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Email <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" name="email" required>
                    </div>
                </div>
            </div>

            <h5 class="mt-4 mb-3">Next of Kin Information</h5>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Next of Kin Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="next_of_kin_name" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Next of Kin Phone <span class="text-danger">*</span></label>
                        <input type="tel" class="form-control" name="next_of_kin_phone" required pattern="[0-9]{11}">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Next of Kin Address <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="next_of_kin_address" required>
                    </div>
                </div>
            </div>

            <h5 class="mt-4 mb-3">Referee Information</h5>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Referee Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="referee_name" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Referee Phone <span class="text-danger">*</span></label>
                        <input type="tel" class="form-control" name="referee_phone" required pattern="[0-9]{11}">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Referee Address <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="referee_address" required>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <button type="submit" class="btn btn-success btn-lg">
                        <i class="fas fa-user-plus"></i> Onboard Customer
                    </button>
                </div>
            </div>
        </form>`;
    }

    // Load banks for dropdown
    function loadBanks() {
        $.get('<?= base_url('admin/customers/banks') ?>', function(banksRes) {
            if (Array.isArray(banksRes)) {
                var options = banksRes.map(function(b) {
                    return `<option value='${b.name}'>${b.name || b.bank_name}</option>`;
                });
                $('#bankDropdown').append(options.join(''));
            }
        }, 'json').fail(function() {
            console.warn('Failed to load banks list');
        });
    }
    // Load loan products on page load
    loadLoanProducts();

    // Handle onboarding form submit
    $(document).on('submit', '#onboardCustomerForm', function(e) {
        e.preventDefault();
        var form = $(this);
        var btn = form.find('button[type="submit"]');
        var originalText = btn.text();

        // Clear previous errors
        form.find('.invalid-feedback').remove();
        form.find('.is-invalid').removeClass('is-invalid');
        form.find('.alert-danger').remove();

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Onboarding...');

        var formData = form.serialize();

        $.ajax({
            url: '<?= base_url('admin/wacs/onboardCustomer') ?>',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(res) {
                if (res.success) {
                    // Extract customer information
                    var customer = (res.data && res.data.customer) ? res.data.customer : null;
                    var customer_id = (customer && customer.id) ? customer.id : (res.data && res.data.customer_id ? res.data.customer_id : '');
                    var ippis = form.find('[name="ippis_number"]').val();
                    var eligibility = form.find('[name="eligibility"]').val();

                    if (customer_id) {
                        // Replace form with loan application form
                        var bvn = form.find('[name="bvn"]').val();
                        var accountNumber = form.find('[name="account_number"]').val();
                        var loanFormHtml = generateLoanForm(customer_id, ippis, parseFloat(eligibility), 'Customer onboarded successfully! Now you can apply for a loan.', bvn, accountNumber);
                        form.replaceWith(loanFormHtml);
                        setupLoanFormHandlers(parseFloat(eligibility));
                    } else {
                        showSuccess(form.parent(), res.message || 'Customer onboarded successfully!');
                    }
                } else {
                    // Handle validation errors
                    if (res.errors && typeof res.errors === 'object') {
                        Object.keys(res.errors).forEach(function(field) {
                            var errorMsg = res.errors[field];
                            var inputName = field;

                            // Map nested error keys to input names
                            if (field.startsWith('next_of_kin.')) {
                                inputName = 'next_of_kin_' + field.split('.')[1];
                            } else if (/^referee\.\d+\.(name|phone|address)$/.test(field)) {
                                var subField = field.split('.')[2];
                                inputName = 'referee_' + subField;
                            }

                            var input = form.find('[name="' + inputName + '"]');
                            if (input.length) {
                                input.addClass('is-invalid');
                                var parent = input.closest('.form-group');
                                parent.append('<div class="invalid-feedback" style="display:block">' + errorMsg + '</div>');
                            }
                        });
                    }

                    // Show general error message
                    var errorMsg = res.message || 'Onboarding failed. Please check the form and try again.';
                    form.prepend('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ' + errorMsg + '</div>');
                }
            },
            error: function(xhr) {
                var errorMsg = 'An error occurred during onboarding. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                form.prepend('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ' + errorMsg + '</div>');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-user-plus"></i> ' + originalText);
            }
        });
    });

    // Handle loan application form submit
    $(document).on('submit', '#loanApplicationForm', function(e) {
        e.preventDefault();
        var form = $(this);
        var btn = form.find('button[type="submit"]');
        var originalText = btn.text();

        // Validate form
        var customerId = form.find('[name="customer_id"]').val();
        var loanProductId = form.find('[name="loan_product_id"]').val();
        var amount = parseFloat(form.find('[name="amount"]').val());

        if (!customerId || !loanProductId || !amount || amount <= 0) {
            form.find('.alert-danger').remove();
            form.prepend('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Please fill in all required fields with valid values.</div>');
            return;
        }

        // Clear previous alerts
        form.find('.alert').remove();
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Applying...');

        var data = {
            customer_id: customerId,
            loan_product_id: loanProductId,
            amount: amount
        };
        data[csrfName] = csrfHash;

        $.ajax({
            url: '<?= base_url('admin/wacs/applyLoan') ?>',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            dataType: 'json',
            success: function(res) {
                if (res.success) {
                    var successMsg = res.message || 'Loan application submitted successfully!';
                    form.replaceWith(`
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> ${successMsg}
                            <br><small>Your loan application is being processed. You will be notified of the status.</small>
                        </div>
                    `);
                } else {
                    var errorMsg = res.message || 'Loan application failed. Please try again.';
                    form.prepend('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ' + errorMsg + '</div>');
                }
            },
            error: function(xhr) {
                var errorMsg = 'An error occurred while processing your loan application. Please try again.';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                } else if (xhr.responseText) {
                    try {
                        var json = JSON.parse(xhr.responseText);
                        if (json && json.message) {
                            errorMsg = json.message;
                        }
                    } catch (e) {
                        // Keep default error message
                    }
                }

                form.prepend('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ' + errorMsg + '</div>');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-paper-plane"></i> ' + originalText);
            }
        });
    });
});
</script>
