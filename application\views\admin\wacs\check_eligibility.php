<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><i class="fas fa-calculator"></i> Check Customer Loan Eligibility</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('admin') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('admin/wacs') ?>">WACS</a></li>
                        <li class="breadcrumb-item active">Check Eligibility</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-12">
                    <div class="card card-primary card-outline">
                       
                        <div class="card-body">
                            <form id="eligibilityForm" method="post" action="#" novalidate>
                                <?php echo form_hidden($this->security->get_csrf_token_name(), $this->security->get_csrf_hash()); ?>
                                <div class="row">
                                    <div class="col-lg-8 col-md-7">
                                        <div class="form-group">
                                            <label for="ippis_number">
                                                <i class="fas fa-id-card text-primary"></i> IPPIS Number
                                            </label>
                                            <div class="input-group input-group-lg">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                                </div>
                                                <input type="text" class="form-control" id="ippis_number" name="ippis_number"
                                                       required pattern="[0-9]+" placeholder="Enter IPPIS number"
                                                       value="<?php echo isset($_GET['ippis_number']) ? htmlspecialchars($_GET['ippis_number']) : ''; ?>">
                                                <div class="invalid-feedback">Please enter a valid IPPIS number.</div>
                                            </div>
                                            <small class="form-text text-muted">Enter the customer's IPPIS number to check loan eligibility</small>
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-5">
                                        <div class="form-group">
                                            <label class="d-none d-md-block">&nbsp;</label> <!-- Spacer for alignment -->
                                            <button type="submit" class="btn btn-primary btn-lg btn-block">
                                                <i class="fas fa-calculator"></i> Check Eligibility
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>

                            <div id="eligibilityResult" class="mt-4" style="display:none;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<script>
$(document).ready(function() {
    // CSRF token setup
    var csrfName = '<?= $this->security->get_csrf_token_name(); ?>';
    var csrfHash = '<?= $this->security->get_csrf_hash(); ?>';

    // Global variables for caching
    var loanProducts = [];
    var productsMap = {};

    // Utility functions
    function formatCurrency(amount) {
        return parseFloat(amount).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2});
    }

    function resetButton(btn, originalText) {
        btn.prop('disabled', false).text(originalText || 'Check Eligibility');
    }

    function showError(resultDiv, message) {
        resultDiv.html('<div class="alert alert-danger">' + message + '</div>').show();
    }

    function showSuccess(resultDiv, message) {
        resultDiv.html('<div class="alert alert-success">' + message + '</div>').show();
    }


  

    // Main eligibility check handler
    $('#eligibilityForm').on('submit', function(e) {
        e.preventDefault();
        var ippis = $('#ippis_number').val().trim();
        var resultDiv = $('#eligibilityResult');
        var btn = $(this).find('button[type="submit"]');

        // Clear previous results
        resultDiv.hide().html('');

        if (!ippis) {
            showError(resultDiv, 'Please enter an IPPIS number.');
            return;
        }

        btn.prop('disabled', true).text('Checking eligibility...');

        // Check eligibility
        $.ajax({
            url: '<?= base_url('admin/wacs/checkEligibility') ?>/' + encodeURIComponent(ippis),
            method: 'GET',
            dataType: 'json',
            success: function(res) {
                // Fix: Check for correct response structure
                var eligibilityAmount = 0;
                var hasEligibility = false;

                if (res && res.success) {
                    // Handle both response formats: direct eligibility or nested in data
                    if (res.eligibility) {
                        eligibilityAmount = parseFloat(res.eligibility) || 0;
                        hasEligibility = true;
                    } else if (res.data && res.data.eligibility >= 0) {
                        eligibilityAmount = parseFloat(res.data.eligibility) || 0;
                        hasEligibility = true;
                    }
                }
				console.log(res);
                if (hasEligibility) {
                    resetButton(btn);
                    var eligibilityDisplay = `
                        <div class="alert alert-default">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>IPPIS Number:</strong> ${ippis}</p>
                                    <p><strong>Eligibility Amount:</strong> <span class="h5 text-success">₦${formatCurrency(eligibilityAmount)}</span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Status:</strong> <span class="badge badge-success">Eligible</span></p>
                                    <p><strong>Date Checked:</strong> ${new Date().toLocaleDateString()}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="text-center">
                                <a href="<?= base_url('admin/wacs/createLoanForm?ippis_number=') ?>${ippis}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Create Loan Application
                                </a>
                                <a href="<?= base_url('admin/wacs/checkOnboardingStatus') ?>" class="btn btn-info ml-2">
                                    <i class="fas fa-user-check"></i> Check Onboarding Status
                                </a>
                            </div>
                        </div>
                    `;
                    resultDiv.html(eligibilityDisplay).show();
                } else {
                    resetButton(btn);
                    var message = (res && res.message) ? res.message : 'Customer not eligible for loan or eligibility amount is zero.';
                    showError(resultDiv, message);
                }
            },
            error: function(xhr) {
                resetButton(btn);
                var message = 'An error occurred while checking eligibility. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showError(resultDiv, message);
            }
        });
    });


});
</script>
