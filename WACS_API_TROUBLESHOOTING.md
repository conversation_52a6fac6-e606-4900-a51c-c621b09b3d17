# WACS API Integration Troubleshooting Guide

## Overview
This guide helps diagnose and resolve issues with the WACS (Workforce Automated Credit System) API integration.

## Common Issues and Solutions

### 1. Laravel Log Permission Errors
**Symptoms:** 
- Error: "The stream or file could not be opened in append mode: Permission denied"
- Multiple repeated error messages in logs

**Solution:**
```bash
# Fix Laravel storage permissions
cd /var/www/wacs
sudo chown -R www-data:www-data storage/
sudo chmod -R 775 storage/
sudo chmod -R 775 bootstrap/cache/

# Create logs directory if missing
sudo mkdir -p storage/logs
sudo chown -R www-data:www-data storage/logs
sudo chmod -R 775 storage/logs

# Restart web server
sudo systemctl restart apache2  # or nginx
```

### 2. WACS API Connection Issues
**Symptoms:**
- "Network Error" messages
- "Authorization failed" errors
- Timeouts during API calls

**Diagnosis Steps:**
1. **Check API Configuration:**
   - Go to Admin → General Settings
   - Verify WACS URLs, usernames, and passwords
   - Ensure correct environment (Test/Live) is selected

2. **Test Basic Connectivity:**
   - Use the API Diagnostics tool: Admin → WACS → API Diagnostics
   - Run "Test Basic Connection"

3. **Test Authentication:**
   - Run "Test Authentication" in diagnostics
   - Check if credentials are valid

**Common Fixes:**
- Verify WACS server is accessible from your server
- Check firewall rules
- Validate SSL certificates if using HTTPS
- Ensure correct API endpoints

### 3. Customer Registration Failures
**Symptoms:**
- "Customer registration failed" messages
- HTTP error codes (400, 401, 403, 500)
- Validation errors

**Diagnosis:**
1. **Check Request Format:**
   - Verify IPPIS number format (numbers only)
   - Verify BVN format (exactly 11 digits)
   - Verify account number format (exactly 10 digits)

2. **Check API Response:**
   - Use API Diagnostics → Test Customer Registration
   - Review detailed error messages
   - Check HTTP status codes

**Common Issues:**
- **HTTP 400:** Invalid request format or missing fields
- **HTTP 401:** Authentication failure
- **HTTP 403:** Insufficient permissions
- **HTTP 422:** Validation errors (duplicate customer, invalid data)
- **HTTP 500:** Server error on WACS side

### 4. Authentication Token Issues
**Symptoms:**
- "Authorization failed. No token received."
- Intermittent authentication failures

**Solutions:**
1. **Check Credentials:**
   - Verify username/password in settings
   - Ensure correct environment (test/live)

2. **Token Caching:**
   - Clear any cached tokens
   - Restart web server

3. **API Endpoint:**
   - Verify login endpoint: `/api/v1/lender/login`
   - Check if endpoint is accessible

### 5. Database Integration Issues
**Symptoms:**
- "Failed to save locally" messages
- Customer data not appearing in local database

**Solutions:**
1. **Check Database Connection:**
   - Verify database credentials in `config/database.php`
   - Test database connectivity

2. **Check Table Structure:**
   - Ensure `onboarded_wacs_customers` table exists
   - Verify table columns match expected fields

3. **Check Model Methods:**
   - Verify `Onboarded_wacs_customers_model` is loaded
   - Check insert/update methods

## Debugging Tools

### 1. Enhanced Logging
The system now includes enhanced logging for WACS API calls:
- Request URLs and payloads
- Response codes and data
- Error details

**Log Location:** `application/logs/`

### 2. API Diagnostics Tool
Access via: Admin → WACS → API Diagnostics

**Features:**
- Test basic connectivity
- Test authentication
- Test customer registration
- Test eligibility checks
- Custom test data input

### 3. Browser Developer Tools
1. Open browser developer tools (F12)
2. Go to Network tab
3. Perform WACS operations
4. Check for failed requests
5. Review request/response details

## Configuration Checklist

### WACS Settings (Admin → General Settings)
- [ ] WACS Live/Test environment correctly selected
- [ ] Base URLs are correct and accessible
- [ ] Usernames are valid
- [ ] Passwords are correct
- [ ] SSL certificates are valid (if using HTTPS)

### Server Configuration
- [ ] PHP cURL extension is enabled
- [ ] SSL support is enabled
- [ ] Firewall allows outbound connections to WACS
- [ ] DNS resolution works for WACS domains

### Database Configuration
- [ ] Database connection is working
- [ ] Required tables exist
- [ ] Proper permissions for database operations

## API Endpoints Reference

### Authentication
- **URL:** `/api/v1/lender/login`
- **Method:** POST
- **Data:** `username`, `password`

### Customer Registration
- **URL:** `/api/v1/lender/customers/register/ussd`
- **Method:** POST
- **Headers:** `Authorization: Bearer {token}`
- **Data:** `ippis_number`, `bvn`, `account_number`

### Eligibility Check
- **URL:** `/api/v1/customer/{ippis}/loan-eligibility`
- **Method:** GET
- **Headers:** `Authorization: Bearer {token}`

### Loan Application Status
- **URL:** `/api/v1/lender/customers/{ippis}/loan-application-status`
- **Method:** GET
- **Headers:** `Authorization: Bearer {token}`

## Error Code Reference

| HTTP Code | Meaning | Common Causes |
|-----------|---------|---------------|
| 200 | Success | Request completed successfully |
| 201 | Created | Resource created successfully |
| 400 | Bad Request | Invalid request format or missing data |
| 401 | Unauthorized | Invalid or missing authentication |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Endpoint or resource not found |
| 422 | Unprocessable Entity | Validation errors |
| 500 | Internal Server Error | Server-side error |
| 502 | Bad Gateway | Proxy/gateway error |
| 503 | Service Unavailable | Service temporarily unavailable |
| 504 | Gateway Timeout | Request timeout |

## Contact Information

For additional support:
1. Check WACS API documentation
2. Contact WACS technical support
3. Review server logs for detailed error information
4. Use the API Diagnostics tool for systematic testing

## Maintenance Tasks

### Regular Checks
- [ ] Monitor API response times
- [ ] Check error logs regularly
- [ ] Verify SSL certificate expiration
- [ ] Test authentication periodically
- [ ] Monitor database performance

### Troubleshooting Workflow
1. **Identify the Issue:** Use error messages and logs
2. **Check Configuration:** Verify all settings
3. **Test Connectivity:** Use diagnostics tools
4. **Review Logs:** Check detailed error information
5. **Test Components:** Isolate the problem area
6. **Apply Fix:** Implement appropriate solution
7. **Verify Resolution:** Test the fix thoroughly
