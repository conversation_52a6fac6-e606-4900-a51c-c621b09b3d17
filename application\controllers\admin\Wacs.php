<?php defined('BASEPATH') or exit('No direct script access allowed');

class Wacs extends MY_Controller {

    public function __construct()
    {

        parent::__construct();
        auth_check(); // check login auth
        $this->rbac->check_module_access();
        $baseUrl = $this->settings->wacs_is_live == 0 ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        define('BASE_URL', $baseUrl);
    }
    
    public function authorizeWacsUser()
    {
        try {
            $baseUrl = $this->settings->wacs_is_live == 0 ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
            $username = $this->settings->wacs_is_live == 0 ? $this->settings->wacs_test_username : $this->settings->wacs_live_username;
            $password = $this->settings->wacs_is_live == 0 ? $this->settings->wacs_test_password : $this->settings->wacs_live_password;
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/login');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(array(
                'username' => $username,
                'password' => $password
            )));
            
            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                $error = 'Network Error: ' . curl_error($ch);
                log_message('error', $error);
                curl_close($ch);
                return null;
            }
            curl_close($ch);
            $response = json_decode($response);
            if (isset($response->data->token)) {
                return $response->data->token;
            } else {
                $error = 'WACS Auth Error: ' . json_encode($response);
                log_message('error', $error);
                return null;
            }
        } catch (Exception $e) {
            log_message('error', 'Exception in authorizeWacsUser: ' . $e->getMessage());
            return null;
        }
    }

    public function checkEligibilityForm() {
        $this->rbac->check_operation_access();
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/check_eligibility');
        $this->load->view('admin/includes/_footer');
    }

    
    public function checkEligibility($ippis_number = null) {
        $this->rbac->check_operation_access();
        if (!$ippis_number) {
            echo json_encode(['success' => false, 'message' => 'IPPIS number is required.']);
            return;
        }
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/customer/' . urlencode($ippis_number) . '/loan-eligibility');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        $json = json_decode($response, true);
        $eligible = null;
        if (isset($json['success']) && $json['success'] && isset($json['data']['eligibility'])) {
            $eligible = $json['data']['eligibility'];
            $this->load->model('admin/Onboarded_wacs_customers_model');

            try {
                $existing = $this->Onboarded_wacs_customers_model->get_by_ippis($ippis_number);
                if ($existing) {
                    // Update existing record
                    $this->Onboarded_wacs_customers_model->update_eligibility_by_ippis($ippis_number, $eligible);
                } else {
                    // Insert new record with proper data structure
                    $insertData = [
                        'ippis_number' => $ippis_number,
                        'eligibility' => $eligible,
                        'current_eligibility' => $eligible,
                        'employee_status' => 'Active', // Default status
                        'created_at' => date('Y-m-d H:i:s')
                    ];

                    // Get table fields to ensure we only insert valid columns
                    $fields = $this->db->list_fields('onboarded_wacs_customers');
                    $validData = [];
                    foreach ($insertData as $field => $value) {
                        if (in_array($field, $fields)) {
                            $validData[$field] = $value;
                        }
                    }

                    if (!empty($validData)) {
                        $result = $this->Onboarded_wacs_customers_model->insert_onboarded_customer($validData);
                        log_message('info', 'Inserted new eligibility record for IPPIS: ' . $ippis_number . ' with ID: ' . $result);
                    }
                }
            } catch (Exception $e) {
                log_message('error', 'Error saving eligibility for IPPIS ' . $ippis_number . ': ' . $e->getMessage());
                // Don't break the response, just log the error
            }
        }
        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
        } else {
            echo $response;
        }
        curl_close($ch);
    }

	/**
     * AJAX endpoint to get onboarded customer by IPPIS number
     */
    public function ajaxGetCustomerByIppis() {
        // Accept both GET and POST parameters for flexibility
        $ippis = $this->input->post('ippis_number', true) ?: $this->input->get('ippis_number', true);
        if (!$ippis) {
            echo json_encode(['success' => false, 'message' => 'IPPIS number required.']);
            return;
        }
        $this->load->model('admin/Onboarded_wacs_customers_model');
        $customer = $this->Onboarded_wacs_customers_model->get_by_ippis($ippis);
        if ($customer) {
            echo json_encode(['success' => true, 'customer' => $customer]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Customer not found.']);
        }
    }

    public function index()
    {
        $this->rbac->check_operation_access();
        // Get stats from onboarded_wacs_customers and wacs_loans tables
        $this->load->database();
        // Customers count
        $stats = [
            'customers' => 0,
            'loans' => 0,
            'total_disbursed' => 0,
            'total_repaid' => 0
        ];
        $stats['customers'] = $this->db->count_all('onboarded_wacs_customers');
        $stats['loans'] = $this->db->count_all('wacs_loans');
        $stats['total_disbursed'] = (float) $this->db->select_sum('disbursed_amount')->get('wacs_loans')->row('disbursed_amount');
        $stats['total_repaid'] = (float) $this->db->select_sum('repayment_amount')->get('wacs_loans')->row('repayment_amount');

        // Get recent loans (last 10)
        $recent_loans = $this->db->order_by('start_date', 'DESC')->limit(10)->get('wacs_loans')->result_array();

        $data = [
            'stats' => $stats,
            'recent_loans' => $recent_loans
        ];
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/dashboard', $data);
        $this->load->view('admin/includes/_footer');
    }


    public function update(){

        if (isset($_POST['updatetype'])) {
                $body = NULL;
                if ($_POST['updatetype'] == 'batch') {
                    
                    $body_arr = NULL;
                    $config['upload_path'] = './uploads/';
                    $config['allowed_types'] = 'csv';
                    $config['max_size'] = 5120; // 5MB
                    $this->load->library('upload', $config);

                    if (!$this->upload->do_upload('csv_file')) {
                        $error = $this->upload->display_errors();
                        echo json_encode(['status'=>false,'message'=>$error]);
                    } else {
                        $csvData = file_get_contents($_FILES['csv_file']['tmp_name']);
                        $rows = explode("\n", $csvData);
                        $totalRecords = 0;
                        foreach ($rows as $row) {
                            $values = str_getcsv($row);
                            if (!empty($values)) {
                                
                            
                                if (count($values) >= 2) {
                                    $totalRecords++;
                                    $body_arr[] =  [
                                        "accountNumber" => $values[0],
                                        "bvn" => $values[1],
                                        "firstName" => $values[2],
                                        "lastName" => $values[3],
                                    ];
                                }
                                
                            }
                        }

                    }
                    $body = $body_arr;
                }elseif($_POST['updatetype'] == 'single'){
                    
                    $body = array(
                        array(
                            "accountNumber" => $this->input->post('nuban'),
                            "firstName" => $this->input->post('firstName'),
                            "lastName" => $this->input->post('lastName'),
                            "bvn" => $this->input->post('bvn')
                        )
                    );
                                
                }
            $headers = array( 
                'x-tag: ****************************************************************************************************************',
                'Content-Type: application/json'
            );
            $json_body = json_encode($body);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://api.veendhq.com/client/consumermicrofinancebak/update-bvn');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

            $response = curl_exec($ch);

            if (curl_errno($ch)) {
                $error = 'Error: ' . curl_error($ch);
                log_message('error', $error);
                echo $error;
            } else {
                echo $response;
            }

            curl_close($ch);
        }else{
            echo json_encode(['status'=>false,'msg'=>'i dont understand your request']);
        }
    }

    public function createLoanProduct() {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        
        if (!$token) {
            $this->session->set_flashdata('error', 'Authorization failed. No token received.');
            redirect($this->agent->referrer());
            return;
        }

        $body = $this->input->post();
        $json_body = json_encode($body);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loanProduct/create');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Accept: application/json',
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false) {
            $this->session->set_flashdata('error', 'Network error occurred while creating loan product.');
            redirect($this->agent->referrer());
            return;
        }

        $result = json_decode($response, true);
        
        if (isset($result['success']) && $result['success']) {
            $msg = isset($result['message']) ? $result['message'] : 'Loan product created successfully.';
            $this->session->set_flashdata('success', $msg);
            redirect($this->agent->referrer());
            return;
        }

        // Handle different types of error responses
        $errorMsg = '';
        if (isset($result['message'])) {
            $errorMsg = $result['message'];
        }

        if (isset($result['errors']) && is_array($result['errors'])) {
            $formattedErrors = [];
            foreach ($result['errors'] as $field => $error) {
                if (is_array($error)) {
                    $formattedErrors[] = is_numeric($field) ? implode('<br>', $error) : $field . ': ' . implode(', ', $error);
                } else {
                    $formattedErrors[] = is_numeric($field) ? $error : $field . ': ' . $error;
                }
            }
            if (!empty($formattedErrors)) {
                $errorMsg .= (!empty($errorMsg) ? '<br>' : '') . implode('<br>', $formattedErrors);
            }
        }

        if (empty($errorMsg)) {
            $errorMsg = 'Error creating loan product. Please check your input and try again.';
        }

        $this->session->set_flashdata('error', $errorMsg);
        redirect($this->agent->referrer());
    }

    public function createLoanProductForm() {
        $this->rbac->check_operation_access();
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/create_loan_product');
        $this->load->view('admin/includes/_footer');
    }

    public function updateLoanProduct($product_id) {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            $this->session->set_flashdata('error', 'Authorization failed. No token received.');
            redirect($this->agent->referrer());
            return;
        }
        $body = $this->input->post();
        // Convert feature to array if comma separated string
        if (isset($body['feature']) && !is_array($body['feature'])) {
            $body['feature'] = array_map('trim', explode(',', $body['feature']));
        }
        $json_body = json_encode($body);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loanProduct/' . $product_id . '/update');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Accept: application/json',
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false) {
            $this->session->set_flashdata('error', 'Network error occurred while updating loan product.');
            redirect($this->agent->referrer());
            return;
        }

        $result = json_decode($response, true);
        if (isset($result['success']) && $result['success']) {
            $msg = isset($result['message']) ? $result['message'] : 'Loan product updated successfully.';
            $this->session->set_flashdata('success', $msg);
            redirect($this->agent->referrer());
            return;
        }
        // Handle different types of error responses
        $errorMsg = '';
        if (isset($result['message'])) {
            $errorMsg = $result['message'];
        }
        if (isset($result['errors']) && is_array($result['errors'])) {
            $formattedErrors = [];
            foreach ($result['errors'] as $field => $error) {
                if (is_array($error)) {
                    $formattedErrors[] = is_numeric($field) ? implode('<br>', $error) : $field . ': ' . implode(', ', $error);
                } else {
                    $formattedErrors[] = is_numeric($field) ? $error : $field . ': ' . $error;
                }
            }
            if (!empty($formattedErrors)) {
                $errorMsg .= (!empty($errorMsg) ? '<br>' : '') . implode('<br>', $formattedErrors);
            }
        }
        if (empty($errorMsg)) {
            $errorMsg = 'Error updating loan product. Please check your input and try again.';
        }
        $this->session->set_flashdata('error', $errorMsg);
        redirect($this->agent->referrer());
    }

    public function listLoanProducts() {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            $data['products'] = [];
            $this->session->set_flashdata('error', 'Authorization failed. No token received.');
        } else {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loanProduct/index');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Authorization: Bearer ' . $token,
                'Accept: application/json',
                'Content-Type: application/json'
            ));
            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                $error = 'Network Error: ' . curl_error($ch);
                log_message('error', $error);
                $data['products'] = [];
                $this->session->set_flashdata('error', $error);
            } else {
                $result = json_decode($response, true);
                $data['products'] = isset($result['data']) ? $result['data'] : [];
            }
            curl_close($ch);
        }
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/list_loan_products', $data);
        $this->load->view('admin/includes/_footer');
    }

    public function editLoanProduct($product_id) {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            $this->session->set_flashdata('error', 'Authorization failed. No token received.');
            redirect($this->agent->referrer());
            return;
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loanProduct/' . $product_id);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Accept: application/json',
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        curl_close($ch);
        $result = json_decode($response, true);
        $product = isset($result['data']) ? $result['data'] : [];
        if (empty($product)) {
            $errorMsg = isset($result['message']) ? $result['message'] : 'Loan product not found.';
            $this->session->set_flashdata('error', $errorMsg);
            redirect($this->agent->referrer());
            return;
        }
        $data['product'] = $product;
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/edit_loan_product', $data);
        $this->load->view('admin/includes/_footer');
    }

    /**
     * Loads the edit loan product form with product details from POST
     */
    public function editLoanProductForm() {
        $this->rbac->check_operation_access();
        $product = $this->input->post('product');
        if (empty($product) || !is_array($product)) {
            $this->session->set_flashdata('error', 'Loan product data not found or invalid.');
            redirect($this->agent->referrer());
            return;
        }
        $data['product'] = $product;
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/edit_loan_product', $data);
        $this->load->view('admin/includes/_footer');
    }

    public function activateLoanProduct($product_id) {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            $this->session->set_flashdata('error', 'Authorization failed. No token received.');
            redirect('admin/wacs/listLoanProducts');
            return;
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loanProduct/' . $product_id . '/activate');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            $this->session->set_flashdata('error', $error);
        } else {
            $result = json_decode($response, true);
            if (isset($result['success']) && $result['success']) {
                $this->session->set_flashdata('success', $result['message'] ?? 'Loan Product Activated Successfully');
            } else {
                $this->session->set_flashdata('error', $result['message'] ?? 'Activation failed.');
            }
        }
        curl_close($ch);
        redirect('admin/wacs/listLoanProducts');
    }

    public function deactivateLoanProduct($product_id) {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            $this->session->set_flashdata('error', 'Authorization failed. No token received.');
            redirect('admin/wacs/listLoanProducts');
            return;
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loanProduct/' . $product_id . '/deactivate');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            $this->session->set_flashdata('error', $error);
        } else {
            $result = json_decode($response, true);
            if (isset($result['success']) && $result['success']) {
                $this->session->set_flashdata('success', $result['message'] ?? 'Loan Product Deactivated Successfully');
            } else {
                $this->session->set_flashdata('error', $result['message'] ?? 'Deactivation failed.');
            }
        }
        curl_close($ch);
        redirect('admin/wacs/listLoanProducts');
    }

       public function allLoans() {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        $loans = [];
        if ($token) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loans');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization: Bearer ' . $token,
                    'Accept: application/json',
                    'Content-Type: application/json'
            ));
            $response = curl_exec($ch);
            if (!curl_errno($ch)) {
                $result = json_decode($response, true);
                if (isset($result['data'])) {
                    $loans = $result['data'];
                }
            } else {
                $error = 'Network Error: ' . curl_error($ch);
                log_message('error', $error);
                $this->session->set_flashdata('error', $error);
            }
            curl_close($ch);
        } else {
            $this->session->set_flashdata('error', 'Authorization failed. No token received.');
        }
        $data = ['loans' => $loans];
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/all_loans', $data);
        $this->load->view('admin/includes/_footer');
    }

      /**
     * AJAX endpoint for paginated, filterable, multi-search WACS loans list
     */
    public function ajaxListLoans() {
        $q = $this->input->get('q', true);
        $date_from = $this->input->get('date_from', true);
        $date_to = $this->input->get('date_to', true);
        $status = $this->input->get('status', true);
        $page = max(1, (int)$this->input->get('page', true));
        $per_page = 20;
        $offset = ($page-1)*$per_page;
        $this->db->from('wacs_loans');
        if ($q) {
            $this->db->group_start()
                ->like('loan_id', $q)
                ->or_like('customer_ippis', $q)
                ->or_like('employee_name', $q)
                ->or_like('debtor', $q)
                ->or_like('phone', $q)
                ->or_like('account_number', $q)
            ->group_end();
        }
        if ($status) $this->db->where('(status_loan = "' . $this->db->escape_str($status) . '" OR status = "' . $this->db->escape_str($status) . '")');
        if ($date_from) $this->db->where('DATE(created_at) >=', $date_from);
        if ($date_to) $this->db->where('DATE(created_at) <=', $date_to);
        $total = $this->db->count_all_results('', false);
        $this->db->order_by('start_date','desc');
        $this->db->limit($per_page, $offset);
        $loans = $this->db->get()->result_array();
        // Pagination
        $this->load->library('pagination');
        $config['base_url'] = base_url('admin/wacs/ajaxListLoans');
        $config['total_rows'] = $total;
        $config['per_page'] = $per_page;
        $config['use_page_numbers'] = true;
        $config['page_query_string'] = true;
        $config['query_string_segment'] = 'page';
        $config['full_tag_open'] = '<ul class="pagination">';
        $config['full_tag_close'] = '</ul>';
        $config['attributes'] = ['class' => 'page-link'];
        $config['cur_tag_open'] = '<li class="page-item active"><a class="page-link" href="#">';
        $config['cur_tag_close'] = '</a></li>';
        $config['num_tag_open'] = '<li class="page-item">';
        $config['num_tag_close'] = '</li>';
        $config['prev_tag_open'] = '<li class="page-item">';
        $config['prev_tag_close'] = '</li>';
        $config['next_tag_open'] = '<li class="page-item">';
        $config['next_tag_close'] = '</li>';
        $config['first_tag_open'] = '<li class="page-item">';
        $config['first_tag_close'] = '</li>';
        $config['last_tag_open'] = '<li class="page-item">';
        $config['last_tag_close'] = '</li>';
        $this->pagination->initialize($config);
        $pagination = $this->pagination->create_links();
        $html = $this->load->view('admin/wacs/_all_loans_table', [
            'loans' => $loans,
            'pagination' => $pagination,
            'total' => $total
        ], true);
        echo $html;
    }

    /**
     * Onboard a new customer to WACS
     * Endpoint: POST /admin/wacs/onboardCustomer
     */
    public function onboardCustomer() {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }
        //var_dump($baseUrl);die();
        // $input = json_decode(file_get_contents('php://input'), true);
        // if (!$input) {
            $input = $this->input->post();
        //}
        
        $body = [
            'ippis_number' => $input['ippis_number'] ?? '',
            'bvn' => $input['bvn'] ?? '',
            'account_number' => $input['account_number'] ?? '',
            'first_name' => $input['first_name'] ?? '',
            'last_name' => $input['last_name'] ?? '',
            'phone_number' => $input['phone_number'] ?? '',
            'email' => $input['email'] ?? '',
            'bank' => $input['bank'] ?? '',
            'next_of_kin' => [
                'name' => $input['next_of_kin']['name'] ?? $input['next_of_kin_name'] ?? '',
                'phone' => $input['next_of_kin']['phone'] ?? $input['next_of_kin_phone'] ?? '',
                'address' => $input['next_of_kin']['address'] ?? $input['next_of_kin_address'] ?? ''
            ],
            'referee' => [
                [
                    'name' => $input['referee'][0]['name'] ?? $input['referee_name'] ?? '',
                    'phone' => $input['referee'][0]['phone'] ?? $input['referee_phone'] ?? '',
                    'address' => $input['referee'][0]['address'] ?? $input['referee_address'] ?? ''
                ]
            ]
        ];
        $json_body = json_encode($body);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/customers/register');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
        } else {
            $result = json_decode($response, true);
            // If onboarding is successful, save to local table
            if (isset($result['success']) && $result['success'] && isset($result['data'])) {
                $this->insertOnboardedWacsCustomer($result['data']);
                echo $response;
            } else {
                // Show all errors, not just summary
                $errorMsg = isset($result['message']) ? $result['message'] : 'Error onboarding customer.';
                $errors = [];
                if (isset($result['errors']) && is_array($result['errors'])) {
                    $formattedErrors = [];
                    foreach ($result['errors'] as $field => $error) {
                        if (is_array($error)) {
                            $formattedErrors[] = is_numeric($field) ? implode('<br>', $error) : $field . ': ' . implode(', ', $error);
                            $errors[$field] = $error;
                        } else {
                            $formattedErrors[] = is_numeric($field) ? $error : $field . ': ' . $error;
                            $errors[$field] = [$error];
                        }
                    }
                    if (!empty($formattedErrors)) {
                        $errorMsg .= (!empty($errorMsg) ? '<br>' : '') . implode('<br>', $formattedErrors);
                    }
                }
                echo json_encode(['success' => false, 'message' => $errorMsg, 'errors' => $errors]);
            }
        }
        curl_close($ch);
    }

    /**
     * Show the standalone WACS customer creation form
     */
    public function createWacsCustomerForm() {
        $this->rbac->check_operation_access();
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/create_wacs_customer');
        $this->load->view('admin/includes/_footer');
    }

    /**
     * Process standalone WACS customer onboarding (AJAX)
     * Endpoint: POST /admin/wacs/createWacsCustomer
     */
    public function createWacsCustomer() {
        $this->rbac->check_operation_access();

        // Get form data
        $ippis_number = $this->input->post('ippis_number', true);
        $bvn = $this->input->post('bvn', true);
        $account_number = $this->input->post('account_number', true);

        // Validate required fields
        $errors = array();

        if (empty($ippis_number)) {
            $errors['ippis_number'] = 'IPPIS number is required.';
        } elseif (!preg_match('/^[0-9]+$/', $ippis_number)) {
            $errors['ippis_number'] = 'IPPIS number must contain only numbers.';
        }

        if (empty($bvn)) {
            $errors['bvn'] = 'BVN is required.';
        } elseif (!preg_match('/^[0-9]{11}$/', $bvn)) {
            $errors['bvn'] = 'BVN must be exactly 11 digits.';
        }

        if (empty($account_number)) {
            $errors['account_number'] = 'Account number is required.';
        } elseif (!preg_match('/^[0-9]{10}$/', $account_number)) {
            $errors['account_number'] = 'Account number must be exactly 10 digits.';
        }

        if (!empty($errors)) {
            echo json_encode(['success' => false, 'errors' => $errors]);
            return;
        }

        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }

        // Prepare API request data for USSD registration
        $body = [
            'ippis_number' => $ippis_number,
            'bvn' => $bvn,
            'account_number' => $account_number
        ];

        $json_body = json_encode($body);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/customers/register/ussd');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json',
            'Cache-Control: no-cache',
            'Pragma: no-cache'
        ));
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
            curl_close($ch);
            return;
        }

        curl_close($ch);

        // Log the response for debugging
        log_message('debug', 'WACS USSD Registration Response: ' . $response);

        $result = json_decode($response, true);

        if ($httpCode == 201 || (isset($result['success']) && $result['success'])) {
            // Customer registered successfully via USSD

            // Check if customer data exists and insert into local database
            if (isset($result['data'])) {
                $this->load->model('admin/Onboarded_wacs_customers_model');

                // Check if customer already exists in local database
                $existing = $this->Onboarded_wacs_customers_model->get_by_ippis($ippis_number);

                if (!$existing) {
                    // Insert customer data into local database
                    $this->insertOnboardedWacsCustomerFromResponse($result['data'], $ippis_number);
                }
            }

            echo json_encode([
                'success' => true,
                'message' => $result['message'] ?? 'Customer registered successfully via USSD!',
                'data' => $result['data'] ?? null
            ]);
        } else {
            // Handle API errors
            $errorMsg = isset($result['message']) ? $result['message'] : 'Customer registration failed.';
            $errors = isset($result['errors']) ? $result['errors'] : null;

            echo json_encode([
                'success' => false,
                'message' => $errorMsg,
                'errors' => $errors
            ]);
        }
    }

    /**
     * Apply for a loan for a customer
     * Endpoint: POST /admin/wacs/applyLoan
     */
    public function applyLoan() {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $this->input->post();
        }
        $body = [
            'customer_id' => $input['customer_id'] ?? '',
            'loan_product_id' => $input['loan_product_id'] ?? '',
            'amount' => $input['amount'] ?? ''
        ];
        $json_body = json_encode($body);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/customers/loan-application');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
            curl_close($ch);
            return;
        }

        curl_close($ch);

        // Parse the response
        $result = json_decode($response, true);

        if ($httpCode == 200 && isset($result['success']) && $result['success'] && isset($result['data']['loan'])) {
            // Save loan data locally
            $this->saveLoanLocally($result['data']['loan']);

            // Return the original response
            echo $response;
        } else {
            // Return the error response
            echo $response;
        }
    }

    /**
     * Save loan data locally to wacs_loans table
     * @param array $loanData - loan data from WACS API response
     * @return int|bool Insert ID or false
     */
    private function saveLoanLocally($loanData) {
        try {
            // Get table fields to ensure we only insert valid columns
            $fields = $this->db->list_fields('wacs_loans');

            // Prepare loan data mapping from API response to database fields
            $dataMapping = [
                'loan_id' => $loanData['loanID'] ?? null,
                'customer_id' => $loanData['customer']['id'] ?? null,
                'customer_ippis' => $loanData['customerIppis'] ?? null,
                'debtor_name' => $loanData['debtor'] ?? null,
                'employee_name' => $loanData['employeeName'] ?? null,
                'loan_product' => $loanData['loanProduct'] ?? null,
                'loan_product_category' => $loanData['loanProductCategory'] ?? null,
                'loan_product_id' => $loanData['loanProductID'] ?? null,
                'interest_rate' => $loanData['interestRate'] ?? null,
                'interest_rate_type' => $loanData['interestRateType'] ?? null,
                'status' => $loanData['status'] ?? null,
                'amount_requested' => $loanData['amountRequested'] ?? null,
                'amount_offered' => $loanData['amountOffered'] ?? null,
                'disbursed_amount' => $loanData['disbursedAmount'] ?? null,
                'repayment_amount' => $loanData['repaymentAmount'] ?? null,
                'repayment_mfb_amount' => $loanData['repaymentMFBAmount'] ?? null,
                'repayment_wacs_amount' => $loanData['repaymentWACsAmount'] ?? null,
                'start_date' => $loanData['startDate'] ?? null,
                'monthly_repayment_amount' => $loanData['monthlyRepaymentAmount'] ?? null,
                'monthly_wacs_repayment_amount' => $loanData['monthlyWACSRepaymentAmount'] ?? null,
                'balance' => $loanData['balance'] ?? 0,
                'amount_paid_so_far' => $loanData['amountPaidSoFar'] ?? 0,
                'loan_tenure' => $loanData['loanTenure'] ?? null,
                'creditor' => $loanData['creditor'] ?? null,
                'moratorium' => $loanData['moratorium'] ?? 0,
                'logo' => $loanData['logo'] ?? null,
                'mda' => $loanData['mda'] ?? null,
                'wacs_loan_id' => $loanData['id'] ?? null,
                'assigned_marketer' => $loanData['assignedMarketer'] ?? null,
                'raw_date' => $loanData['rawDate'] ?? null
            ];

            // Only include fields that exist in the table
            $localLoanData = [];
            foreach ($dataMapping as $field => $value) {
                if (in_array($field, $fields)) {
                    $localLoanData[$field] = $value;
                }
            }

            // Insert into wacs_loans table
            if (!empty($localLoanData)) {
                $insertId = $this->db->insert('wacs_loans', $localLoanData);

                if ($insertId) {
                    log_message('info', 'Loan saved locally with ID: ' . $this->db->insert_id() . ' for loan_id: ' . ($loanData['loanID'] ?? 'unknown'));
                    return $this->db->insert_id();
                } else {
                    log_message('error', 'Failed to save loan locally for loan_id: ' . ($loanData['loanID'] ?? 'unknown') . '. Database error: ' . $this->db->error()['message']);
                    return false;
                }
            } else {
                log_message('error', 'No valid fields to insert for loan_id: ' . ($loanData['loanID'] ?? 'unknown'));
                return false;
            }

        } catch (Exception $e) {
            log_message('error', 'Exception saving loan locally: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * AJAX endpoint to get all loan products (for dropdowns)
     * Returns only active products
     */
    public function getLoanProducts() {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        $products = [];
        if ($token) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loanProduct/index');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization: Bearer ' . $token,
                    'Accept: application/json',
                    'Content-Type: application/json'
            ));
            $response = curl_exec($ch);
            if (!curl_errno($ch)) {
                $result = json_decode($response, true);
                if (isset($result['data'])) {
                    // Only active products
                    $products = array_filter($result['data'], function($p) {
                        return (isset($p['status']) && ($p['status'] === 'active' || $p['status'] === 1)) || (isset($p['is_active']) && $p['is_active'] == 1);
                    });
                }
            }
            curl_close($ch);
        }
        echo json_encode(['success' => true, 'data' => array_values($products)]);
    }

    /**
     * Insert onboarded WACS customer into local table
     * @param array $customerData - associative array of customer fields (from WACS onboarding response)
     * @param string|null $created_by - username or id of admin creating (optional)
     * @return int|bool Insert ID or false
     */
    private function insertOnboardedWacsCustomer($customerData, $created_by = null) {
        $this->load->model('admin/Onboarded_wacs_customers_model');
        $data = [
            'mda' => $customerData['mda'] ?? null,
            'pfa_name' => $customerData['pfa_name'] ?? null,
            'account_name' => $customerData['account_name'] ?? null,
            'account_number' => $customerData['account_number'] ?? null,
            'bank' => $customerData['bank'] ?? null,
            'bank_code' => $customerData['bank_code'] ?? null,
            'bvn' => $customerData['bvn'] ?? null,
            'current_salary' => $customerData['current_salary'] ?? null,
            'current_eligibility' => $customerData['current_eligibility'] ?? null,
            'ippis_number' => $customerData['ippis_number'] ?? null,
            'nationality' => $customerData['nationality'] ?? null,
            'address' => $customerData['address'] ?? null,
            'state' => $customerData['state'] ?? null,
            'employee_status' => $customerData['employee_status'] ?? null,
            'first_name' => $customerData['first_name'] ?? null,
            'last_name' => $customerData['last_name'] ?? null,
            'middle_name' => $customerData['middle_name'] ?? null,
            'phone_number' => $customerData['phone_number'] ?? null,
            'email' => $customerData['email'] ?? null,
            'role' => $customerData['role'] ?? null,
            'created_by' => $created_by ?? ($this->session->userdata('id') ?? null),
        ];
        return $this->Onboarded_wacs_customers_model->insert_onboarded_customer($data);
    }

    public function listOnboardedCustomers() {
        $this->rbac->check_operation_access();
        $this->load->model('admin/Onboarded_wacs_customers_model');
        $customers = $this->Onboarded_wacs_customers_model->db->order_by('id','desc')->get('onboarded_wacs_customers')->result_array();
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/list_onboarded_customers', ['customers' => $customers]);
        $this->load->view('admin/includes/_footer');
    }

    public function ajaxListOnboardedCustomers() {
        $this->load->model('admin/Onboarded_wacs_customers_model');
        $q = $this->input->get('q', true);
        $date_from = $this->input->get('date_from', true);
        $date_to = $this->input->get('date_to', true);
        $page = max(1, (int)$this->input->get('page', true));
        $per_page = 20;
        $offset = ($page-1)*$per_page;
        $this->db->from('onboarded_wacs_customers');
        if ($q) {
            $this->db->group_start()
                ->like('ippis_number', $q)
                ->or_like('first_name', $q)
                ->or_like('last_name', $q)
                ->or_like('phone_number', $q)
                ->or_like('account_number', $q)
            ->group_end();
        }
        if ($date_from) $this->db->where('DATE(created_at) >=', $date_from);
        if ($date_to) $this->db->where('DATE(created_at) <=', $date_to);
        $total = $this->db->count_all_results('', false);
        $this->db->order_by('id','desc');
        $this->db->limit($per_page, $offset);
        $customers = $this->db->get()->result_array();
        // Pagination
        $this->load->library('pagination');
        $config['base_url'] = base_url('admin/wacs/ajaxListOnboardedCustomers');
        $config['total_rows'] = $total;
        $config['per_page'] = $per_page;
        $config['use_page_numbers'] = true;
        $config['page_query_string'] = true;
        $config['query_string_segment'] = 'page';
        $config['full_tag_open'] = '<ul class="pagination">';
        $config['full_tag_close'] = '</ul>';
        $config['attributes'] = ['class' => 'page-link'];
        $config['cur_tag_open'] = '<li class="page-item active"><a class="page-link" href="#">';
        $config['cur_tag_close'] = '</a></li>';
        $config['num_tag_open'] = '<li class="page-item">';
        $config['num_tag_close'] = '</li>';
        $config['prev_tag_open'] = '<li class="page-item">';
        $config['prev_tag_close'] = '</li>';
        $config['next_tag_open'] = '<li class="page-item">';
        $config['next_tag_close'] = '</li>';
        $config['first_tag_open'] = '<li class="page-item">';
        $config['first_tag_close'] = '</li>';
        $config['last_tag_open'] = '<li class="page-item">';
        $config['last_tag_close'] = '</li>';
        $this->pagination->initialize($config);
        $pagination = $this->pagination->create_links();
        $html = $this->load->view('admin/wacs/_onboarded_customers_table', [
            'customers' => $customers,
            'pagination' => $pagination,
            'total' => $total,
            'offset' => $offset
        ], true);
        echo $html;
    }

    /**
     * Register customer via USSD endpoint
     * Endpoint: POST /admin/wacs/registerCustomerUssd
     */
    public function registerCustomerUssd() {
        $this->rbac->check_operation_access();

        // Get input data
        $ippis_number = $this->input->post('ippis_number', true);
        $bvn = $this->input->post('bvn', true);
        $account_number = $this->input->post('account_number', true);

        // Validate required fields and formats
        $errors = [];

        if (!$ippis_number) {
            $errors['ippis_number'] = 'IPPIS number is required.';
        } elseif (!preg_match('/^[0-9]+$/', $ippis_number)) {
            $errors['ippis_number'] = 'IPPIS number must contain only digits.';
        }

        if (!$bvn) {
            $errors['bvn'] = 'BVN is required.';
        } elseif (!preg_match('/^[0-9]{11}$/', $bvn)) {
            $errors['bvn'] = 'BVN must be exactly 11 digits.';
        }

        if (!$account_number) {
            $errors['account_number'] = 'Account number is required.';
        } elseif (!preg_match('/^[0-9]{10}$/', $account_number)) {
            $errors['account_number'] = 'Account number must be exactly 10 digits.';
        }

        if (!empty($errors)) {
            echo json_encode([
                'success' => false,
                'message' => 'Please correct the following errors:',
                'errors' => $errors
            ]);
            return;
        }

        // Get WACS API configuration
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();

        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }

        // Prepare request data
        $requestData = [
            'ippis_number' => $ippis_number,
            'bvn' => $bvn,
            'account_number' => $account_number
        ];

        $json_body = json_encode($requestData);

        // Make API call to WACS
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl . '/api/v1/lender/customers/register/ussd');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
            curl_close($ch);
            return;
        }

        curl_close($ch);

        $result = json_decode($response, true);

        if ($httpCode == 201 && isset($result['success']) && $result['success'] && isset($result['data'])) {
            // Save customer data to local database
            $saved = $this->saveOnboardedWacsCustomer($result['data']);

            if ($saved) {
                // Return the successful response
                echo $response;
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Customer registered successfully but failed to save locally. Please contact administrator.'
                ]);
            }
        } else {
            // Handle API errors
            $errorMsg = isset($result['message']) ? $result['message'] : 'Customer registration failed.';

            if (isset($result['errors']) && is_array($result['errors'])) {
                $errorDetails = [];
                foreach ($result['errors'] as $field => $error) {
                    if (is_array($error)) {
                        $errorDetails[] = implode(', ', $error);
                    } else {
                        $errorDetails[] = $error;
                    }
                }
                if (!empty($errorDetails)) {
                    $errorMsg .= ' Details: ' . implode('; ', $errorDetails);
                }
            }

            echo json_encode(['success' => false, 'message' => $errorMsg]);
        }
    }

    /**
     * Save onboarded WACS customer data to local database
     */
    private function saveOnboardedWacsCustomer($data) {
        try {
            $this->load->model('admin/Onboarded_wacs_customers_model');

            $customer = $data['customer'];
            $user = $customer['user'];
            $moreInfo = $data['more_info'];

            // Prepare data for insertion
            $insertData = [
                'customer_id' => $customer['id'],
                'mda' => $customer['mda'],
                'pfa_name' => $customer['pfaName'],
                'account_name' => $customer['accountName'],
                'account_number' => $customer['accountNumber'],
                'bank' => $customer['bank'],
                'bank_code' => $customer['bankCode'],
                'bvn' => $customer['bvn'],
                'gender' => $customer['gender'],
                'current_salary' => $customer['currentSalary'],
                'ippis_number' => $customer['ippisNumber'],
                'nationality' => $customer['nationality'],
                'address' => $customer['address'],
                'state' => $customer['state'],
                'employee_status' => $customer['employeeStatus'],

                // User information
                'user_id' => $user['id'],
                'user_first_name' => $user['firstName'],
                'user_last_name' => $user['lastName'],
                'user_middle_name' => $user['middleName'],
                'user_phone_number' => $user['phoneNumber'],
                'user_email' => $user['email'],
                'user_role' => $user['role'],

                // More info
                'more_info_id' => $moreInfo['id'],
                'data_source' => $moreInfo['data_source'],
                'reference_id' => $moreInfo['reference_id'],
                'more_info_ippis_number' => $moreInfo['ippis_number'],
                'staff_id' => $moreInfo['staff_id'],
                'title' => $moreInfo['title'],
                'first_name' => $moreInfo['first_name'],
                'middle_name' => $moreInfo['middle_name'],
                'surname' => $moreInfo['surname'],
                'full_name' => $moreInfo['full_name'],
                'date_of_birth' => $moreInfo['date_of_birth'],
                'date_of_hire' => $moreInfo['date_of_hire'],
                'marital_status' => $moreInfo['marital_status'],
                'more_info_gender' => $moreInfo['gender'],
                'mobile_number' => $moreInfo['mobile_number'],
                'email_address' => $moreInfo['email_address'],
                'more_info_nationality' => $moreInfo['nationality'],
                'mother_maiden_name' => $moreInfo['mother_maiden_name'],
                'lga_of_origin' => $moreInfo['lga_of_origin'],
                'home_town' => $moreInfo['home_town'],
                'residential_address' => $moreInfo['residential_address'],
                'residential_country' => $moreInfo['residential_country'],
                'residential_state' => $moreInfo['residential_state'],
                'residential_lga' => $moreInfo['residential_lga'],
                'residential_city' => $moreInfo['residential_city'],
                'more_info_mda' => $moreInfo['mda'],
                'department' => $moreInfo['department'],
                'rank' => $moreInfo['rank'],
                'cadre' => $moreInfo['cadre'],
                'grade_name' => $moreInfo['grade_name'],
                'grade_level' => $moreInfo['grade_level'],
                'grade_step' => $moreInfo['grade_step'],
                'date_of_appointment' => $moreInfo['date_of_appointment'],
                'expected_date_of_retirement' => $moreInfo['expected_date_of_retirement'],
                'enrollment_date' => $moreInfo['enrollment_date'],
                'date_of_confirmation' => $moreInfo['date_of_confirmation'],
                'section' => $moreInfo['section'],
                'unit' => $moreInfo['unit'],
                'more_info_pfa_name' => $moreInfo['pfa_name'],
                'pfa_pin' => $moreInfo['pfa_pin'],
                'nhf_number' => $moreInfo['nhf_number'],
                'bank_type' => $moreInfo['bank_type'],
                'bank_name' => $moreInfo['bank_name'],
                'more_info_bank_code' => $moreInfo['bank_code'],
                'bank_branch' => $moreInfo['bank_branch'],
                'more_info_account_number' => $moreInfo['account_number'],
                'account_type' => $moreInfo['account_type'],
                'sort_code' => $moreInfo['sort_code'],
                'payroll_name' => $moreInfo['payroll_name'],
                'trade_union' => $moreInfo['trade_union'],
                'tax_id' => $moreInfo['tax_id'],
                'tax_state' => $moreInfo['tax_state'],
                'payroll_period' => $moreInfo['payroll_period'],
                'employment_status' => $moreInfo['employment_status'],
                'created_at' => $moreInfo['created_at'],
                'updated_at' => $moreInfo['updated_at'],
                'more_info_eligibility' => $moreInfo['eligibility'],
                'eligibility' => $data['eligibility'],
                'response_json' => json_encode($data)
            ];

            // Check if customer already exists
            $existing = $this->Onboarded_wacs_customers_model->get_by_ippis($customer['ippisNumber']);

            if ($existing) {
                // Update existing record
                return $this->Onboarded_wacs_customers_model->update_by_ippis($customer['ippisNumber'], $insertData);
            } else {
                // Insert new record
                return $this->Onboarded_wacs_customers_model->insert_onboarded_customer($insertData);
            }

        } catch (Exception $e) {
            log_message('error', 'Error saving onboarded WACS customer: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Display create loan form view
     */
    public function createLoanForm() {
        $this->rbac->check_operation_access();
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/create_loan_form');
        $this->load->view('admin/includes/_footer');
    }

    /**
     * Get customer details by ID
     * Endpoint: GET /admin/wacs/getCustomerDetails/{id}
     */
    public function getCustomerDetails($customer_id = null) {
        $this->rbac->check_operation_access();

        if (!$customer_id) {
            echo json_encode(['success' => false, 'message' => 'Customer ID is required.']);
            return;
        }

        $this->load->model('admin/Onboarded_wacs_customers_model');
        $customer = $this->db->get_where('onboarded_wacs_customers', ['id' => $customer_id])->row_array();

        if ($customer) {
            echo json_encode(['success' => true, 'customer' => $customer]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Customer not found.']);
        }
    }

    /**
     * Get customer details by IPPIS from local database
     * Endpoint: GET /admin/wacs/getLocalCustomerByIppis/{ippis}
     */
    public function getLocalCustomerByIppis($ippis_number = null) {
        $this->rbac->check_operation_access();

        if (!$ippis_number) {
            echo json_encode(['success' => false, 'message' => 'IPPIS number is required.']);
            return;
        }

        $this->load->model('admin/Onboarded_wacs_customers_model');
        $customer = $this->Onboarded_wacs_customers_model->get_by_ippis($ippis_number);

        if ($customer) {
            echo json_encode(['success' => true, 'customer' => $customer]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Customer not found in local database.']);
        }
    }

    /**
     * Get customer onboarding status by IPPIS
     * Endpoint: GET /admin/wacs/getCustomerOnboardingStatus/{ippis}
     */
    public function getCustomerOnboardingStatus($ippis_number = null) {
        $this->rbac->check_operation_access();

        if (!$ippis_number) {
            echo json_encode(['success' => false, 'message' => 'IPPIS number is required.']);
            return;
        }

        // Get WACS API configuration
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();

        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }

        // Make API call to get customer onboarding status
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl . '/api/v1/lender/customers/' . urlencode($ippis_number) . '/onboarding-status');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json',
        ));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
            curl_close($ch);
            return;
        }

        curl_close($ch);

        $result = json_decode($response, true);

        if ($httpCode == 201) {
            // Check if customer exists (either success=true OR message indicates enrollment)
            if ((isset($result['success']) && $result['success']) ||
                (isset($result['message']) && stripos($result['message'], 'already enrolled') !== false)) {
				
                // If customer exists in API response, update/insert into local database
                if (isset($result['data'])) {
                    $this->updateOrInsertOnboardedCustomer($result['data'], $ippis_number);
                }

                // Return the successful response as-is
                echo $response;
            } else {
                // Handle API errors
                $errorMsg = isset($result['message']) ? $result['message'] : 'Customer onboarding status not found.';
                echo json_encode(['success' => false, 'message' => $errorMsg]);
            }
        } else {
            // HTTP error
            $errorMsg = isset($result['message']) ? $result['message'] : 'Failed to get customer onboarding status.';
            echo json_encode(['success' => false, 'message' => $errorMsg]);
        }
    }

    /**
     * Get customer data from WACS API using alternative endpoint
     * This is used when onboarding-status doesn't return customer data
     */
    private function getCustomerFromWacsApi($ippis_number) {
        // Get WACS API configuration
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();

        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }

        // Try different endpoints to get customer data
        $endpoints = [
            '/api/v1/lender/customers/' . urlencode($ippis_number),
            '/api/v1/customer/' . urlencode($ippis_number) . '/details',
            '/api/v1/lender/customers/search?ippis=' . urlencode($ippis_number)
        ];

        foreach ($endpoints as $endpoint) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl . $endpoint);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Authorization: Bearer ' . $token,
                'Accept: application/json'
            ));

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode == 201) {
                $result = json_decode($response, true);
                if (isset($result['success']) && $result['success'] && isset($result['data'])) {
                    // Found customer data, return it
                    echo $response;
                    return;
                }
            }
        }

        // If all endpoints fail, return error
        echo json_encode([
            'success' => false,
            'message' => 'Customer exists but detailed information could not be retrieved from WACS API. Please contact administrator.'
        ]);
    }

    /**
     * Get loan details by ID
     * Endpoint: GET /admin/wacs/getLoanDetails/{loan_id}
     */
    public function getLoanDetails($loan_id = null) {
        $this->rbac->check_operation_access();

        if (!$loan_id) {
            echo json_encode(['success' => false, 'message' => 'Loan ID is required.']);
            return;
        }

        // Get WACS API configuration
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();

        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }

        // Make API call to get loan details
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl . '/api/v1/lender/loans/' . urlencode($loan_id));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Cache-Control: no-cache'
        ));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
            curl_close($ch);
            return;
        }

        curl_close($ch);

        if ($httpCode == 200) {
            echo $response;
        } else {
            $result = json_decode($response, true);
            $errorMsg = isset($result['message']) ? $result['message'] : 'Failed to get loan details.';
            echo json_encode(['success' => false, 'message' => $errorMsg]);
        }
    }

    /**
     * Accept/Approve loan
     * Endpoint: PATCH /admin/wacs/acceptLoan/{loan_id}
     */
    public function acceptLoan($loan_id = null) {
        $this->rbac->check_operation_access();

        if (!$loan_id) {
            echo json_encode(['success' => false, 'message' => 'Loan ID is required.']);
            return;
        }

        // Get WACS API configuration
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();

        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }

        // Make API call to accept loan
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl . '/api/v1/lender/loan-request/accept/' . urlencode($loan_id) . '/');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json',
            'Cache-Control: no-cache'
        ));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
            curl_close($ch);
            return;
        }

        curl_close($ch);

        if ($httpCode == 200) {
            echo $response;
        } else {
            $result = json_decode($response, true);
            $errorMsg = isset($result['message']) ? $result['message'] : 'Failed to accept loan.';
            echo json_encode(['success' => false, 'message' => $errorMsg]);
        }
    }

    /**
     * Reject/Decline loan
     * Endpoint: POST /admin/wacs/rejectLoan/{loan_id}
     */
    public function rejectLoan($loan_id = null) {
        $this->rbac->check_operation_access();

        if (!$loan_id) {
            echo json_encode(['success' => false, 'message' => 'Loan ID is required.']);
            return;
        }

        // Get rejection reason from POST data
        $reason = $this->input->post('reason', true) ?: 'No reason provided';

        // Get WACS API configuration
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();

        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }

        // Prepare request data
        $requestData = ['reason' => $reason];
        $json_body = json_encode($requestData);

        // Make API call to reject loan
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl . '/api/v1/lender/loan-request/reject/' . urlencode($loan_id));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json',
            'Cache-Control: no-cache'
        ));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
            curl_close($ch);
            return;
        }

        curl_close($ch);

        if ($httpCode == 200) {
            echo $response;
        } else {
            $result = json_decode($response, true);
            $errorMsg = isset($result['message']) ? $result['message'] : 'Failed to reject loan.';
            echo json_encode(['success' => false, 'message' => $errorMsg]);
        }
    }

    /**
     * Liquidate/Terminate loan
     * Endpoint: POST /admin/wacs/liquidateLoan
     */
    public function liquidateLoan() {
        $this->rbac->check_operation_access();

        // Get required data from POST
        $loan_id = $this->input->post('loan_id', true);
        $customer_id = $this->input->post('customer_id', true);
        $note = $this->input->post('note', true) ?: 'Loan liquidated by administrator';

        if (!$loan_id || !$customer_id) {
            echo json_encode(['success' => false, 'message' => 'Loan ID and Customer ID are required.']);
            return;
        }

        // Get WACS API configuration
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();

        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }

        // Prepare request data
        $requestData = [
            'loan_id' => $loan_id,
            'customer_id' => $customer_id,
            'note' => $note
        ];
        $json_body = json_encode($requestData);

        // Make API call to liquidate loan
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl . '/api/v1/lender/loan-liquidation/create');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json',
            'Cache-Control: no-cache'
        ));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
            curl_close($ch);
            return;
        }

        curl_close($ch);

        if ($httpCode == 200) {
            echo $response;
        } else {
            $result = json_decode($response, true);
            $errorMsg = isset($result['message']) ? $result['message'] : 'Failed to liquidate loan.';
            echo json_encode(['success' => false, 'message' => $errorMsg]);
        }
    }

    /**
     * Display check onboarding status view
     */
    public function checkOnboardingStatus() {
        $this->rbac->check_operation_access();
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/check_onboarding_status');
        $this->load->view('admin/includes/_footer');
    }

    /**
     * Update or insert customer data from API response into onboarded_wacs_customers table
     * @param array $apiData - Customer data from WACS API response
     * @param string $ippis_number - IPPIS number as key for updating
     */
    private function updateOrInsertOnboardedCustomer($apiData, $ippis_number) {
        try {
            $this->load->model('admin/Onboarded_wacs_customers_model');

            // Map API response data to database fields
            $customerData = $this->mapApiDataToCustomerFields($apiData, $ippis_number);
			
            // Use upsert method to update if exists or insert if not
            $result = $this->Onboarded_wacs_customers_model->upsert_by_ippis($customerData);
			//var_dump($result); die();
            if ($result) {
                log_message('info', 'Successfully updated/inserted customer data for IPPIS: ' . $ippis_number);
            } else {
                log_message('error', 'Failed to update/insert customer data for IPPIS: ' . $ippis_number);
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'Exception in updateOrInsertOnboardedCustomer for IPPIS ' . $ippis_number . ': ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Insert customer data from createWacsCustomer response into onboarded_wacs_customers table
     * @param array $apiData - Customer data from WACS API response
     * @param string $ippis_number - IPPIS number
     */
    private function insertOnboardedWacsCustomerFromResponse($apiData, $ippis_number) {
        try {
            $this->load->model('admin/Onboarded_wacs_customers_model');

            // Map API response data to database fields
            $customerData = $this->mapApiDataToCustomerFields($apiData, $ippis_number);

            // Insert customer data
            $result = $this->Onboarded_wacs_customers_model->insert_onboarded_customer($customerData);

            if ($result) {
                log_message('info', 'Successfully inserted customer data for IPPIS: ' . $ippis_number . ' with ID: ' . $result);
            } else {
                log_message('error', 'Failed to insert customer data for IPPIS: ' . $ippis_number);
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'Exception in insertOnboardedWacsCustomerFromResponse for IPPIS ' . $ippis_number . ': ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Map API response data to database fields for onboarded_wacs_customers table
     * @param array $apiData - Customer data from WACS API response
     * @param string $ippis_number - IPPIS number
     * @return array - Mapped data for database insertion
     */
    private function mapApiDataToCustomerFields($apiData, $ippis_number) {
        // Initialize mapped data array
        $mappedData = [
            'ippis_number' => $ippis_number,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Map customer data if available
        if (isset($apiData['customer'])) {
            $customer = $apiData['customer'];
            $mappedData['customer_id'] = $customer['id'] ?? null;
            $mappedData['mda'] = $customer['mda'] ?? null;
            $mappedData['pfa_name'] = $customer['pfaName'] ?? null;
            $mappedData['account_name'] = $customer['accountName'] ?? null;
            $mappedData['account_number'] = $customer['accountNumber'] ?? null;
            $mappedData['bank'] = $customer['bank'] ?? null;
            $mappedData['bank_code'] = $customer['bankCode'] ?? null;
            $mappedData['bvn'] = $customer['bvn'] ?? null;
            $mappedData['gender'] = $customer['gender'] ?? null;
            $mappedData['current_salary'] = $customer['currentSalary'] ?? null;
            $mappedData['nationality'] = $customer['nationality'] ?? null;
            $mappedData['address'] = $customer['address'] ?? null;
            $mappedData['state'] = $customer['state'] ?? null;
            $mappedData['employee_status'] = $customer['employeeStatus'] ?? null;

            // Map user data if available
            if (isset($customer['user'])) {
                $user = $customer['user'];
                $mappedData['user_id'] = $user['id'] ?? null;
                $mappedData['user_first_name'] = $user['firstName'] ?? null;
                $mappedData['user_last_name'] = $user['lastName'] ?? null;
                $mappedData['user_middle_name'] = $user['middleName'] ?? null;
                $mappedData['user_phone_number'] = $user['phoneNumber'] ?? null;
                $mappedData['user_email'] = $user['email'] ?? null;
                $mappedData['user_role'] = $user['role'] ?? null;
            }
        }

        // Map more_info data if available
        if (isset($apiData['more_info'])) {
            $moreInfo = $apiData['more_info'];
            $mappedData['more_info_id'] = $moreInfo['id'] ?? null;
            $mappedData['data_source'] = $moreInfo['data_source'] ?? null;
            $mappedData['reference_id'] = $moreInfo['reference_id'] ?? null;
            $mappedData['more_info_ippis_number'] = $moreInfo['ippis_number'] ?? null;
            $mappedData['staff_id'] = $moreInfo['staff_id'] ?? null;
            $mappedData['title'] = $moreInfo['title'] ?? null;
            $mappedData['first_name'] = $moreInfo['first_name'] ?? null;
            $mappedData['middle_name'] = $moreInfo['middle_name'] ?? null;
            $mappedData['surname'] = $moreInfo['surname'] ?? null;
            $mappedData['full_name'] = $moreInfo['full_name'] ?? null;
            $mappedData['date_of_birth'] = $moreInfo['date_of_birth'] ?? null;
            $mappedData['date_of_hire'] = $moreInfo['date_of_hire'] ?? null;
            $mappedData['marital_status'] = $moreInfo['marital_status'] ?? null;
            $mappedData['more_info_gender'] = $moreInfo['gender'] ?? null;
            $mappedData['mobile_number'] = $moreInfo['mobile_number'] ?? null;
            $mappedData['email_address'] = $moreInfo['email_address'] ?? null;
            $mappedData['more_info_nationality'] = $moreInfo['nationality'] ?? null;
            $mappedData['mother_maiden_name'] = $moreInfo['mother_maiden_name'] ?? null;
            $mappedData['lga_of_origin'] = $moreInfo['lga_of_origin'] ?? null;
            $mappedData['home_town'] = $moreInfo['home_town'] ?? null;
            $mappedData['residential_address'] = $moreInfo['residential_address'] ?? null;
            $mappedData['residential_country'] = $moreInfo['residential_country'] ?? null;
            $mappedData['residential_state'] = $moreInfo['residential_state'] ?? null;
            $mappedData['residential_lga'] = $moreInfo['residential_lga'] ?? null;
            $mappedData['residential_city'] = $moreInfo['residential_city'] ?? null;
            $mappedData['more_info_mda'] = $moreInfo['mda'] ?? null;
            $mappedData['department'] = $moreInfo['department'] ?? null;
            $mappedData['rank'] = $moreInfo['rank'] ?? null;
            $mappedData['cadre'] = $moreInfo['cadre'] ?? null;
            $mappedData['grade_name'] = $moreInfo['grade_name'] ?? null;
            $mappedData['grade_level'] = $moreInfo['grade_level'] ?? null;
            $mappedData['grade_step'] = $moreInfo['grade_step'] ?? null;
            $mappedData['date_of_appointment'] = $moreInfo['date_of_appointment'] ?? null;
            $mappedData['expected_date_of_retirement'] = $moreInfo['expected_date_of_retirement'] ?? null;
            $mappedData['enrollment_date'] = $moreInfo['enrollment_date'] ?? null;
            $mappedData['date_of_confirmation'] = $moreInfo['date_of_confirmation'] ?? null;
            $mappedData['section'] = $moreInfo['section'] ?? null;
            $mappedData['unit'] = $moreInfo['unit'] ?? null;
            $mappedData['more_info_pfa_name'] = $moreInfo['pfa_name'] ?? null;
            $mappedData['pfa_pin'] = $moreInfo['pfa_pin'] ?? null;
            $mappedData['nhf_number'] = $moreInfo['nhf_number'] ?? null;
            $mappedData['bank_type'] = $moreInfo['bank_type'] ?? null;
            $mappedData['bank_name'] = $moreInfo['bank_name'] ?? null;
            $mappedData['more_info_bank_code'] = $moreInfo['bank_code'] ?? null;
            $mappedData['bank_branch'] = $moreInfo['bank_branch'] ?? null;
            $mappedData['more_info_account_number'] = $moreInfo['account_number'] ?? null;
            $mappedData['account_type'] = $moreInfo['account_type'] ?? null;
            $mappedData['sort_code'] = $moreInfo['sort_code'] ?? null;
            $mappedData['payroll_name'] = $moreInfo['payroll_name'] ?? null;
            $mappedData['trade_union'] = $moreInfo['trade_union'] ?? null;
            $mappedData['tax_id'] = $moreInfo['tax_id'] ?? null;
            $mappedData['tax_state'] = $moreInfo['tax_state'] ?? null;
            $mappedData['payroll_period'] = $moreInfo['payroll_period'] ?? null;
            $mappedData['employment_status'] = $moreInfo['employment_status'] ?? null;
            $mappedData['more_info_eligibility'] = $moreInfo['eligibility'] ?? null;
        }

        // Map eligibility data if available
        if (isset($apiData['eligibility'])) {
            $mappedData['eligibility'] = $apiData['eligibility'];
        }

        // Store the complete response as JSON for reference
        $mappedData['response_json'] = json_encode($apiData);

        return $mappedData;
    }

    /**
     * Check if customer exists in local database, if not call getCustomerOnboardingStatus to populate
     * @param string $ippis_number - IPPIS number to check
     * @return array|null - Customer data if found, null if not found
     */
    public function checkCustomerExistsOrFetch($ippis_number = null) {
        $this->rbac->check_operation_access();

        if (!$ippis_number) {
            echo json_encode(['success' => false, 'message' => 'IPPIS number is required.']);
            return;
        }

        $this->load->model('admin/Onboarded_wacs_customers_model');

        // Check if customer exists in local database
        $customer = $this->Onboarded_wacs_customers_model->get_by_ippis($ippis_number);

        if ($customer) {
            // Customer exists, return the data
            echo json_encode(['success' => true, 'message' => 'Customer found in local database.', 'customer' => $customer]);
        } else {
            // Call getCustomerOnboardingStatus to populate customer data
            $this->getCustomerOnboardingStatus($ippis_number);
        }
    }

}

