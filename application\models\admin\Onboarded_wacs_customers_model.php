<?php
class Onboarded_wacs_customers_model extends CI_Model {

    public function insert_onboarded_customer($data) {
        try {
            // Check if IPPIS already exists to prevent duplicates
            if (isset($data['ippis_number'])) {
                $existing = $this->get_by_ippis($data['ippis_number']);
                if ($existing) {
                    // Update instead of insert to avoid duplicate key error
                    $this->update_by_ippis($data['ippis_number'], $data);
                    return $existing['id'];
                }
            }

            $this->db->insert('onboarded_wacs_customers', $data);
            $insert_id = $this->db->insert_id();

            if (!$insert_id) {
                log_message('error', 'Failed to insert onboarded customer: ' . $this->db->error()['message']);
                return false;
            }

            return $insert_id;
        } catch (Exception $e) {
            log_message('error', 'Exception in insert_onboarded_customer: ' . $e->getMessage());
            return false;
        }
    }

    public function update_eligibility_by_ippis($ippis_number, $eligible_amount) {
        try {
            $this->db->where('ippis_number', $ippis_number);

            // Get table fields to ensure we only update valid columns
            $fields = $this->db->list_fields('onboarded_wacs_customers');
            $updateData = [];

            if (in_array('eligibility', $fields)) {
                $updateData['eligibility'] = $eligible_amount;
            }
            if (in_array('current_eligibility', $fields)) {
                $updateData['current_eligibility'] = $eligible_amount;
            }
            if (in_array('updated_at', $fields)) {
                $updateData['updated_at'] = date('Y-m-d H:i:s');
            }

            if (empty($updateData)) {
                log_message('error', 'No valid eligibility fields found in table for IPPIS: ' . $ippis_number);
                return false;
            }

            $result = $this->db->update('onboarded_wacs_customers', $updateData);

            if (!$result) {
                log_message('error', 'Failed to update eligibility for IPPIS ' . $ippis_number . ': ' . $this->db->error()['message']);
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'Exception updating eligibility for IPPIS ' . $ippis_number . ': ' . $e->getMessage());
            return false;
        }
    }

    public function update_by_ippis($ippis_number, $data) {
        $this->db->where('ippis_number', $ippis_number);
        return $this->db->update('onboarded_wacs_customers', $data);
    }

    public function get_by_ippis($ippis_number) {
        return $this->db->get_where('onboarded_wacs_customers', ['ippis_number' => $ippis_number])->row_array();
    }

    public function get_by_customer_id($customer_id) {
        return $this->db->get_where('onboarded_wacs_customers', ['customer_id' => $customer_id])->row_array();
    }

    /**
     * Insert or update customer record based on IPPIS number
     * @param array $data Customer data
     * @return int|bool Insert ID or update result
     */
    public function upsert_by_ippis($data) {
        if (!isset($data['ippis_number'])) {
            log_message('error', 'IPPIS number is required for upsert operation');
            return false;
        }

        $ippis_number = $data['ippis_number'];
        $existing = $this->get_by_ippis($ippis_number);

        if ($existing) {
            // Update existing record
            $result = $this->update_by_ippis($ippis_number, $data);
            return $result ? $existing['id'] : false;
        } else {
            // Insert new record
            return $this->insert_onboarded_customer($data);
        }
    }


}
