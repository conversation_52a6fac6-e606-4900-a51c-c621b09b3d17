<div class="table-responsive">
    <table class="table table-hover">
        <thead class="thead-light">
            <tr>
                <th>Customer</th>
                <th><PERSON>an <PERSON>ails</th>
                <th>Financial Info</th>
                <th>Status</th>
                <th>Date</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
        <?php if (!empty($loans)) : foreach ($loans as $loan) : ?>
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="user-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mr-3" style="width: 40px; height: 40px; font-size: 14px;">
                            <?= strtoupper(substr($loan['employee_name'] ?? $loan['debtor'] ?? 'N', 0, 2)) ?>
                        </div>
                        <div>
                            <div class="font-weight-bold"><?= htmlspecialchars($loan['employee_name'] ?? $loan['debtor'] ?? 'N/A') ?></div>
                            <small class="text-muted">IPPIS: <?= htmlspecialchars($loan['customer_ippis'] ?? 'N/A') ?></small>
                            <?php if (!empty($loan['phone'])): ?>
                                <br><small class="text-muted"><?= htmlspecialchars($loan['phone']) ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                </td>
                <td>
                    <div>
                        <div class="font-weight-bold text-primary"><?= htmlspecialchars($loan['loan_id'] ?? $loan['id']) ?></div>
                        <small class="text-muted"><?= htmlspecialchars($loan['loan_product'] ?? 'N/A') ?></small>
                        <?php if (!empty($loan['loan_product_category'])): ?>
                            <br><small class="text-muted"><?= htmlspecialchars($loan['loan_product_category']) ?></small>
                        <?php endif; ?>
                    </div>
                </td>
                <td>
                    <div>
                        <div><strong>Requested:</strong> ₦<?= number_format($loan['amount_requested'] ?? 0, 2) ?></div>
                        <div><strong>Disbursed:</strong> ₦<?= number_format($loan['disbursed_amount'] ?? 0, 2) ?></div>
                        <?php if (!empty($loan['monthly_repayment_amount'])): ?>
                            <small class="text-muted">Monthly: ₦<?= number_format($loan['monthly_repayment_amount'], 2) ?></small>
                        <?php endif; ?>
                    </div>
                </td>
                <td>
                    <?php
                    $status = strtolower($loan['status_loan'] ?? $loan['status'] ?? '');
                    $badgeClass = 'secondary';
                    switch($status) {
                        case 'approved':
                        case 'awaiting disbursal':
                        case 'active':
                            $badgeClass = 'success';
                            break;
                        case 'pending':
                        case 'processing':
                            $badgeClass = 'warning';
                            break;
                        case 'rejected':
                        case 'declined':
                            $badgeClass = 'danger';
                            break;
                        case 'liquidated':
                        case 'closed':
                            $badgeClass = 'info';
                            break;
                    }
                    ?>
                    <span class="badge badge-<?= $badgeClass ?> px-2 py-1">
                        <?= ucfirst(htmlspecialchars($loan['status_loan'] ?? $loan['status'] ?? 'Unknown')) ?>
                    </span>
                </td>
                <td>
                    <span class="text-muted"><?= date('M d, Y', strtotime($loan['start_date'] ?? $loan['created_at'] ?? 'now')) ?></span>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary loan-action-btn"
                                data-loan-id="<?= htmlspecialchars($loan['loan_id'] ?? $loan['id']) ?>"
                                data-customer-id="<?= htmlspecialchars($loan['customer_id'] ?? '') ?>"
                                data-action="view" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <?php
                        $loanStatus = strtolower($loan['status_loan'] ?? $loan['status'] ?? '');
                        $isAwaitingDisbursal = $loanStatus === 'awaiting disbursal';
                        $isApproved = in_array($loanStatus, ['approved', 'awaiting disbursal', 'disbursed', 'active']);
                        ?>
                        <?php if (!$isAwaitingDisbursal): ?>
                        <button type="button" class="btn btn-sm btn-outline-success loan-action-btn"
                                data-loan-id="<?= htmlspecialchars($loan['loan_id'] ?? $loan['id']) ?>"
                                data-customer-id="<?= htmlspecialchars($loan['customer_id'] ?? '') ?>"
                                data-action="approve" title="Approve">
                            <i class="fas fa-check"></i>
                        </button>
                        <?php endif; ?>
                        <?php if (!$isApproved): ?>
                        <button type="button" class="btn btn-sm btn-outline-danger loan-action-btn"
                                data-loan-id="<?= htmlspecialchars($loan['loan_id'] ?? $loan['id']) ?>"
                                data-customer-id="<?= htmlspecialchars($loan['customer_id'] ?? '') ?>"
                                data-action="reject" title="Reject">
                            <i class="fas fa-times"></i>
                        </button>
                        <?php endif; ?>
                        <button type="button" class="btn btn-sm btn-outline-warning loan-action-btn"
                                data-loan-id="<?= htmlspecialchars($loan['loan_id'] ?? $loan['id']) ?>"
                                data-customer-id="<?= htmlspecialchars($loan['customer_id'] ?? '') ?>"
                                data-action="liquidate" title="Liquidate">
                            <i class="fas fa-money-bill"></i>
                        </button>
                    </div>
                </td>
            </tr>
        <?php endforeach; else: ?>
            <tr><td colspan="6" class="text-center py-4">
                <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                <p class="text-muted">No loans found.</p>
            </td></tr>
        <?php endif; ?>
        </tbody>
    </table>
</div>
<div class="d-flex justify-content-between align-items-center mt-2">
    <div><b>Total:</b> <?= $total ?></div>
    <div><?= $pagination ?></div>
</div>
<!-- Loan Details Modal -->
<div class="modal fade" id="loanDetailsModal" tabindex="-1" role="dialog" aria-labelledby="loanDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="loanDetailsModalLabel">
                    <i class="fas fa-file-alt"></i> Loan Details
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="loanDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loan Approval Modal -->
<div class="modal fade" id="loanApprovalModal" tabindex="-1" role="dialog" aria-labelledby="loanApprovalModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="loanApprovalModalLabel">
                    <i class="fas fa-check-circle text-success"></i> Approve Loan
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Are you sure you want to approve this loan? This action cannot be undone.
                </div>
                <div id="approvalLoanInfo">
                    <!-- Loan info will be populated here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmApproval">
                    <i class="fas fa-check"></i> Approve Loan
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Loan Rejection Modal -->
<div class="modal fade" id="loanRejectionModal" tabindex="-1" role="dialog" aria-labelledby="loanRejectionModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="loanRejectionModalLabel">
                    <i class="fas fa-times-circle text-danger"></i> Reject Loan
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="loanRejectionForm">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Are you sure you want to reject this loan? Please provide a reason.
                    </div>
                    <div id="rejectionLoanInfo">
                        <!-- Loan info will be populated here -->
                    </div>
                    <div class="form-group">
                        <label for="rejection_reason">Rejection Reason <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="rejection_reason" name="reason" rows="3" required
                                  placeholder="Please provide a detailed reason for rejecting this loan..."></textarea>
                        <div class="invalid-feedback">Please provide a reason for rejection.</div>
                    </div>
                    <input type="hidden" id="rejection_loan_id" name="loan_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i> Reject Loan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Loan Liquidation Modal -->
<div class="modal fade" id="loanLiquidationModal" tabindex="-1" role="dialog" aria-labelledby="loanLiquidationModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="loanLiquidationModalLabel">
                    <i class="fas fa-money-bill text-warning"></i> Liquidate Loan
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="loanLiquidationForm">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Are you sure you want to liquidate this loan? This action will terminate the loan.
                    </div>
                    <div id="liquidationLoanInfo">
                        <!-- Loan info will be populated here -->
                    </div>
                    <div class="form-group">
                        <label for="liquidation_note">Liquidation Note</label>
                        <textarea class="form-control" id="liquidation_note" name="note" rows="3"
                                  placeholder="Optional note about the liquidation..."></textarea>
                    </div>
                    <input type="hidden" id="liquidation_loan_id" name="loan_id">
                    <input type="hidden" id="liquidation_customer_id" name="customer_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-money-bill"></i> Liquidate Loan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
$(function(){
    var currentLoanId = null;
    var currentCustomerId = null;

    // Loan action button handler
    $(document).on('click', '.loan-action-btn', function() {
        var loanId = $(this).data('loan-id');
        var customerId = $(this).data('customer-id');
        var action = $(this).data('action');

        currentLoanId = loanId;
        currentCustomerId = customerId;

        switch(action) {
            case 'view':
                showLoanDetails(loanId);
                break;
            case 'approve':
                showApprovalModal(loanId);
                break;
            case 'reject':
                showRejectionModal(loanId);
                break;
            case 'liquidate':
                showLiquidationModal(loanId, customerId);
                break;
        }
    });

    // Show loan details modal
    function showLoanDetails(loanId) {
        $('#loanDetailsModal').modal('show');
        $('#loanDetailsContent').html(`
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">Loading loan details...</p>
            </div>
        `);

        // Get loan details and application status
        Promise.all([
            $.ajax({
                url: '<?= base_url('admin/wacs/getLoanDetails') ?>/' + loanId,
                method: 'GET',
                dataType: 'json'
            }),
            getLoanApplicationStatus(loanId)
        ]).then(function(responses) {
            var loanDetailsResponse = responses[0];
            var applicationStatusResponse = responses[1];

            if (loanDetailsResponse.success && loanDetailsResponse.data && loanDetailsResponse.data.loan) {
                displayLoanDetails(loanDetailsResponse.data.loan, applicationStatusResponse);
            } else {
                $('#loanDetailsContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${loanDetailsResponse.message || 'Failed to load loan details.'}
                    </div>
                `);
            }
        }).catch(function() {
            $('#loanDetailsContent').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    An error occurred while loading loan details.
                </div>
            `);
        });
    }

    // Get loan application status by IPPIS number
    function getLoanApplicationStatus(loanId) {
        // First get the loan details to extract IPPIS number
        return $.ajax({
            url: '<?= base_url('admin/wacs/getLoanDetails') ?>/' + loanId,
            method: 'GET',
            dataType: 'json'
        }).then(function(response) {
            if (response.success && response.data && response.data.loan && response.data.loan.customerIppis) {
                var ippisNumber = response.data.loan.customerIppis;
                return $.ajax({
                    url: '<?= base_url('admin/wacs/getLoanApplicationStatus') ?>/' + ippisNumber,
                    method: 'GET',
                    dataType: 'json'
                });
            }
            return null;
        }).catch(function() {
            return null;
        });
    }

    // Display loan details (same as dashboard)
    function displayLoanDetails(loan, applicationStatusResponse) {
        var html = `
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-info-circle"></i> Loan Information</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr><td><strong>Loan ID:</strong></td><td>${loan.loanID || 'N/A'}</td></tr>
                                <tr><td><strong>Debtor:</strong></td><td>${loan.debtor || loan.employeeName || 'N/A'}</td></tr>
                                <tr><td><strong>IPPIS:</strong></td><td>${loan.customerIppis || 'N/A'}</td></tr>
                                <tr><td><strong>MDA:</strong></td><td>${loan.mda || 'N/A'}</td></tr>
                                <tr><td><strong>Product:</strong></td><td>${loan.loanProduct || 'N/A'}</td></tr>
                                <tr><td><strong>Category:</strong></td><td>${loan.loanProductCategory || 'N/A'}</td></tr>
                                <tr><td><strong>Status:</strong></td><td><span class="badge badge-info">${loan.status || 'N/A'}</span></td></tr>
                                <tr><td><strong>Creditor:</strong></td><td>${loan.creditor || 'N/A'}</td></tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-money-bill-wave"></i> Financial Details</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr><td><strong>Amount Requested:</strong></td><td>₦${parseFloat(loan.amountRequested || 0).toLocaleString()}</td></tr>
                                <tr><td><strong>Amount Offered:</strong></td><td>₦${parseFloat(loan.amountOffered || 0).toLocaleString()}</td></tr>
                                <tr><td><strong>Disbursed Amount:</strong></td><td>₦${parseFloat(loan.disbursedAmount || 0).toLocaleString()}</td></tr>
                                <tr><td><strong>Interest Rate:</strong></td><td>${loan.interestRate || 0}% ${loan.interestRateType || 'yearly'}</td></tr>
                                <tr><td><strong>Tenure:</strong></td><td>${loan.loanTenure || 0} months</td></tr>
                                <tr><td><strong>Moratorium:</strong></td><td>${loan.moratorium || 0} month(s)</td></tr>
                                <tr><td><strong>Balance:</strong></td><td>₦${parseFloat(loan.balance || 0).toLocaleString()}</td></tr>
                                <tr><td><strong>Amount Paid:</strong></td><td>₦${parseFloat(loan.amountPaidSoFar || 0).toLocaleString()}</td></tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-calendar-alt"></i> Repayment Breakdown</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="info-box bg-primary">
                                        <span class="info-box-icon"><i class="fas fa-money-bill"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Total Repayment</span>
                                            <span class="info-box-number">₦${parseFloat(loan.repaymentAmount || 0).toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="info-box bg-success">
                                        <span class="info-box-icon"><i class="fas fa-calendar"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Monthly Repayment</span>
                                            <span class="info-box-number">₦${parseFloat(loan.monthlyRepaymentAmount || 0).toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="info-box bg-warning">
                                        <span class="info-box-icon"><i class="fas fa-chart-line"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">WACS Monthly</span>
                                            <span class="info-box-number">₦${parseFloat(loan.monthlyWACSRepaymentAmount || 0).toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="info-box bg-info">
                                        <span class="info-box-icon"><i class="fas fa-university"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">MFB Repayment Amount</span>
                                            <span class="info-box-number">₦${parseFloat(loan.repaymentMFBAmount || 0).toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-box bg-secondary">
                                        <span class="info-box-icon"><i class="fas fa-building"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">WACS Repayment Amount</span>
                                            <span class="info-box-number">₦${parseFloat(loan.repaymentWACsAmount || 0).toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Start Date:</strong> ${loan.startDate ? new Date(loan.startDate).toLocaleDateString() : 'N/A'} |
                                        <strong>Created:</strong> ${loan.createdAt || 'N/A'}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add application status data if available
        if (applicationStatusResponse && applicationStatusResponse.success && applicationStatusResponse.data && applicationStatusResponse.data.loans) {
            var applicationLoans = applicationStatusResponse.data.loans;
            var currentLoan = applicationLoans.find(l => l.loanID === loan.loanID);

            if (currentLoan && currentLoan.customer) {
                html += `
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-user-circle"></i> Customer Application Status</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-sm">
                                                <tr><td><strong>Current Salary:</strong></td><td>₦${parseFloat(currentLoan.customer.current_salary || 0).toLocaleString()}</td></tr>
                                                <tr><td><strong>Current Eligibility:</strong></td><td>₦${parseFloat(currentLoan.customer.current_eligibility || 0).toLocaleString()}</td></tr>
                                                <tr><td><strong>Bank:</strong></td><td>${currentLoan.customer.bank || 'N/A'}</td></tr>
                                                <tr><td><strong>Account Number:</strong></td><td>${currentLoan.customer.account_number || 'N/A'}</td></tr>
                                                <tr><td><strong>BVN:</strong></td><td>${currentLoan.customer.bvn || 'N/A'}</td></tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-sm">
                                                <tr><td><strong>Employee Status:</strong></td><td><span class="badge badge-success">${currentLoan.customer.employee_status || 'N/A'}</span></td></tr>
                                                <tr><td><strong>Phone Verified:</strong></td><td>${currentLoan.customer.phone_number_verified_at ? new Date(currentLoan.customer.phone_number_verified_at).toLocaleDateString() : 'Not verified'}</td></tr>
                                                <tr><td><strong>State:</strong></td><td>${currentLoan.customer.state || 'N/A'}</td></tr>
                                                <tr><td><strong>Bank Code:</strong></td><td>${currentLoan.customer.bank_code || 'N/A'}</td></tr>
                                                <tr><td><strong>Last Updated:</strong></td><td>${currentLoan.customer.updated_at ? new Date(currentLoan.customer.updated_at).toLocaleDateString() : 'N/A'}</td></tr>
                                            </table>
                                        </div>
                                    </div>

                                    ${currentLoan.customer.user ? `
                                    <div class="row mt-3">
                                        <div class="col-md-12">
                                            <h6><i class="fas fa-user"></i> User Information</h6>
                                            <table class="table table-sm">
                                                <tr><td><strong>Full Name:</strong></td><td>${currentLoan.customer.user.first_name || ''} ${currentLoan.customer.user.middle_name || ''} ${currentLoan.customer.user.last_name || ''}</td></tr>
                                                <tr><td><strong>Email:</strong></td><td>${currentLoan.customer.user.email || 'N/A'}</td></tr>
                                                <tr><td><strong>Phone:</strong></td><td>${currentLoan.customer.user.phone_number || 'N/A'}</td></tr>
                                                <tr><td><strong>Role:</strong></td><td><span class="badge badge-info">${currentLoan.customer.user.role || 'N/A'}</span></td></tr>
                                                <tr><td><strong>Email Verified:</strong></td><td>${currentLoan.customer.user.email_verified_at ? new Date(currentLoan.customer.user.email_verified_at).toLocaleDateString() : 'Not verified'}</td></tr>
                                            </table>
                                        </div>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        $('#loanDetailsContent').html(html);
    }

    // Show approval modal
    function showApprovalModal(loanId) {
        $('#approvalLoanInfo').html(`<p><strong>Loan ID:</strong> ${loanId}</p>`);
        $('#loanApprovalModal').modal('show');
    }

    // Show rejection modal
    function showRejectionModal(loanId) {
        $('#rejection_loan_id').val(loanId);
        $('#rejectionLoanInfo').html(`<p><strong>Loan ID:</strong> ${loanId}</p>`);
        $('#loanRejectionModal').modal('show');
    }

    // Show liquidation modal
    function showLiquidationModal(loanId, customerId) {
        $('#liquidation_loan_id').val(loanId);
        $('#liquidation_customer_id').val(customerId);
        $('#liquidationLoanInfo').html(`
            <p><strong>Loan ID:</strong> ${loanId}</p>
            <p><strong>Customer ID:</strong> ${customerId}</p>
        `);
        $('#loanLiquidationModal').modal('show');
    }

    // Handle loan approval
    $('#confirmApproval').on('click', function() {
        var btn = $(this);
        var originalText = btn.html();

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Approving...');

        $.ajax({
            url: '<?= base_url('admin/wacs/acceptLoan') ?>/' + currentLoanId,
            method: 'PATCH',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#loanApprovalModal').modal('hide');
                    showNotification('success', response.message || 'Loan approved successfully!');
                    setTimeout(function() { loadLoans(); }, 1500);
                } else {
                    // Check for specific "Invoice already generated" message
                    if (response.message && response.message.toLowerCase().includes('invoice already generated')) {
                        $('#loanApprovalModal').modal('hide');
                        showNotification('info', 'Approval has already been granted for this loan. The invoice has been generated.');
                        setTimeout(function() { loadLoans(); }, 1500);
                    } else {
                        showNotification('error', response.message || 'Failed to approve loan.');
                    }
                }
            },
            error: function() {
                showNotification('error', 'An error occurred while approving the loan.');
            },
            complete: function() {
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Handle loan rejection
    $('#loanRejectionForm').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var btn = form.find('button[type="submit"]');
        var originalText = btn.html();

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Rejecting...');

        var formData = form.serialize();
        var loanId = $('#rejection_loan_id').val();

        $.ajax({
            url: '<?= base_url('admin/wacs/rejectLoan') ?>/' + loanId,
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#loanRejectionModal').modal('hide');
                    showNotification('success', response.message || 'Loan rejected successfully!');
                    setTimeout(function() { loadLoans(); }, 1500);
                } else {
                    showNotification('error', response.message || 'Failed to reject loan.');
                }
            },
            error: function() {
                showNotification('error', 'An error occurred while rejecting the loan.');
            },
            complete: function() {
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Handle loan liquidation
    $('#loanLiquidationForm').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var btn = form.find('button[type="submit"]');
        var originalText = btn.html();

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Liquidating...');

        var formData = form.serialize();

        $.ajax({
            url: '<?= base_url('admin/wacs/liquidateLoan') ?>',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#loanLiquidationModal').modal('hide');
                    showNotification('success', response.message || 'Loan liquidated successfully!');
                    setTimeout(function() { loadLoans(); }, 1500);
                } else {
                    showNotification('error', response.message || 'Failed to liquidate loan.');
                }
            },
            error: function() {
                showNotification('error', 'An error occurred while liquidating the loan.');
            },
            complete: function() {
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Notification function
    function showNotification(type, message) {
        var alertClass = type === 'success' ? 'alert-success' : (type === 'info' ? 'alert-info' : 'alert-danger');
        var icon = type === 'success' ? 'fa-check-circle' : (type === 'info' ? 'fa-info-circle' : 'fa-exclamation-triangle');

        var notification = $(`
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                <i class="fas ${icon}"></i> ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `);

        $('body').append(notification);

        setTimeout(function() {
            notification.alert('close');
        }, 5000);
    }
});
</script>
