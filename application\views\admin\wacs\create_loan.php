<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Create New WACS Loan</h1>
                </div>
            </div>
        </div>
    </section>
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <form id="createLoanForm">
                                <?php echo form_hidden($this->security->get_csrf_token_name(), $this->security->get_csrf_hash()); ?>
                                <div class="form-group">
                                    <label for="customer_id">Customer</label>
                                    <select class="form-control" id="customer_id" name="customer_id" required>
                                        <option value="">Select Customer</option>
                                        <?php if(isset($customers) && is_array($customers)) foreach($customers as $c): ?>
                                            <option value="<?= htmlspecialchars($c['id']) ?>"><?= htmlspecialchars($c['first_name'] . ' ' . $c['last_name'] . ' (' . $c['ippis_number'] . ')') ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="product_id">Loan Product</label>
                                    <select class="form-control" id="product_id" name="product_id" required>
                                        <option value="">Select Product</option>
                                        <?php if(isset($products) && is_array($products)) foreach($products as $p): ?>
                                            <option value="<?= htmlspecialchars($p['id']) ?>"><?= htmlspecialchars($p['name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="amount">Amount</label>
                                    <input type="number" class="form-control" id="amount" name="amount" required>
                                </div>
                                <button type="submit" class="btn btn-primary">Create Loan</button>
                            </form>
                            <div id="createLoanResult" class="mt-4" style="display:none;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<script>
$(document).ready(function() {
    $('#createLoanForm').on('submit', function(e) {
        e.preventDefault();
        var formData = $(this).serialize();
        $('#createLoanResult').hide().html('');
        $.ajax({
            url: base_url + 'admin/wacs/createLoan',
            method: 'POST',
            data: formData,
            dataType: 'json',
            beforeSend: function() {
                $('#createLoanResult').html('<div class="alert alert-info">Creating loan...</div>').show();
            },
            success: function(res) {
                if (res.success) {
                    $('#createLoanResult').html('<div class="alert alert-success">' + (res.message || 'Loan created successfully!') + '</div>').show();
                } else {
                    $('#createLoanResult').html('<div class="alert alert-warning">' + (res.message || 'Loan creation failed.') + '</div>').show();
                }
            },
            error: function(xhr) {
                var msg = 'An error occurred while creating loan.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    msg = xhr.responseJSON.message;
                }
                $('#createLoanResult').html('<div class="alert alert-danger">' + msg + '</div>').show();
            }
        });
    });
});
</script>
