<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="content-wrapper">
	<div class="card">
		<div class="card-body">
			<?php $this->load->view('admin/includes/_messages.php') ?>
			<div class="container">
				<div class="row justify-content-center">
					<div class="">
						<h2>Create Loan Product</h2>
						<form id="createLoanProductForm" action="<?php echo base_url('admin/wacs/createLoanProduct'); ?>" method="post" class="needs-validation" novalidate>
							<?php echo form_hidden($this->security->get_csrf_token_name(), $this->security->get_csrf_hash()); ?>
							<div class="row">
								<div class="form-group col-md-3">
									<label for="product_id">Product ID*</label>
									<input type="text" class="form-control" name="product_id" id="product_id" required>
									<div class="invalid-feedback">Please provide a product ID.</div>
								</div>
								<div class="form-group col-md-3">
									<label for="title">Title*</label>
									<input type="text" class="form-control" name="title" id="title" required>
									<div class="invalid-feedback">Please provide a title.</div>
								</div>
								<div class="form-group col-md-3">
									<label for="description">Description*</label>
									<textarea class="form-control" name="description" id="description" required></textarea>
									<div class="invalid-feedback">Please provide a description.</div>
								</div>
								<div class="form-group col-md-3">
									<label for="product_image">Product Image URL</label>
									<input type="url" class="form-control" name="product_image" id="product_image">
									<div class="invalid-feedback">Please provide a valid URL.</div>
								</div>
								<div class="form-group col-md-3">
									<label for="category">Category*</label>
									<input type="text" class="form-control" name="category" id="category" required>
									<div class="invalid-feedback">Please provide a category.</div>
								</div>
								<div class="form-group col-md-3">
									<label for="feature">Features (comma separated)</label>
									<input type="text" class="form-control" name="feature" id="feature" placeholder="Feature 1, Feature 2, Feature 3">
								</div>
								<div class="form-group col-md-3">
									<label for="interest_rate">Interest Rate (%)* </label>
									<input type="number" step="0.01" min="0" max="100" class="form-control" name="interest_rate" id="interest_rate" required>
									<div class="invalid-feedback">Please provide a valid interest rate (0-100).</div>
								</div>
								<div class="form-group col-md-3">
									<label for="amount_from">Amount From*</label>
									<input type="number" min="0" class="form-control" name="amount_from" id="amount_from" required>
									<div class="invalid-feedback">Please provide a valid minimum amount.</div>
								</div>
								<div class="form-group col-md-3">
									<label for="amount_to">Amount To*</label>
									<input type="number" min="0" class="form-control" name="amount_to" id="amount_to" required>
									<div class="invalid-feedback">Please provide a valid maximum amount.</div>
								</div>
								<div class="form-group col-md-3">
									<label for="moratorium_period">Moratorium Period (months)*</label>
									<input type="number" min="0" class="form-control" name="moratorium_period" id="moratorium_period" required>
									<div class="invalid-feedback">Please provide a valid moratorium period.</div>
								</div>
								<div class="form-group col-md-3">
									<label for="payback_period">Tenure (months)*</label>
									<input type="number" min="1" class="form-control" name="payback_period" id="payback_period" required>
									<div class="invalid-feedback">Please provide a valid payback period.</div>
								</div>
								<div class="form-group col-md-3">
									<label for="bank_charge_type">Bank Charge Type</label>
									<select class="form-control" name="bank_charge_type" id="bank_charge_type" >
										<option value="">Select charge type</option>
										<option value="0">Flat</option>
										<option value="1">Percentage</option>
									</select>
									<div class="invalid-feedback">Please select a charge type.</div>
								</div>
								<div class="form-group col-md-3">
									<label for="bank_charge_value">Bank Charge Value</label>
									<input type="number" min="0" step="0.01" class="form-control" name="bank_charge_value" id="bank_charge_value" required>
									<div class="invalid-feedback">Please provide a valid charge value.</div>
								</div>
								<div class="col-md-12 mt-3">
									<button type="submit" class="btn btn-primary">Create Loan Product</button>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
(function() {
	'use strict';
	
	// Fetch all forms that need validation
	var forms = document.querySelectorAll('.needs-validation');
	
	// Loop over them and prevent submission
	Array.prototype.slice.call(forms).forEach(function(form) {
		form.addEventListener('submit', function(event) {
			if (!form.checkValidity()) {
				event.preventDefault();
				event.stopPropagation();
			}
			form.classList.add('was-validated');
		}, false);
	});

	// Additional custom validations
	document.getElementById('amount_to').addEventListener('change', function() {
		var amountFrom = parseFloat(document.getElementById('amount_from').value);
		var amountTo = parseFloat(this.value);
		if (amountTo <= amountFrom) {
			this.setCustomValidity('Amount To must be greater than Amount From');
		} else {
			this.setCustomValidity('');
		}
	});

	document.getElementById('interest_rate').addEventListener('input', function() {
		var value = parseFloat(this.value);
		if (value < 0 || value > 100) {
			this.setCustomValidity('Interest rate must be between 0 and 100');
		} else {
			this.setCustomValidity('');
		}
	});

	document.getElementById('bank_charge_value').addEventListener('input', function() {
		var chargeType = document.getElementById('bank_charge_type').value;
		var value = parseFloat(this.value);
		if (chargeType === '1' && value > 100) { // Percentage type
			this.setCustomValidity('Percentage charge cannot exceed 100%');
		} else {
			this.setCustomValidity('');
		}
	});
})();
</script>
