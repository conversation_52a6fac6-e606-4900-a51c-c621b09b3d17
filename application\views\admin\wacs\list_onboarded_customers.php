<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><i class="fas fa-users"></i> Onboarded WACS Customers</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('admin') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('admin/wacs') ?>">WACS</a></li>
                        <li class="breadcrumb-item active">Customers</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-list"></i> Customer Management
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('admin/wacs/createWacsCustomerForm') ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add New Customer
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <form id="searchForm" class="form-inline">
                                <div class="input-group input-group-sm mr-2 mb-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    </div>
                                    <input type="text" class="form-control" id="search_query" placeholder="Search by IPPIS, Name, Phone, Account No">
                                </div>
                                <div class="input-group input-group-sm mr-2 mb-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                    </div>
                                    <input type="date" class="form-control" id="date_from" placeholder="From">
                                </div>
                                <div class="input-group input-group-sm mr-2 mb-2">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                    </div>
                                    <input type="date" class="form-control" id="date_to" placeholder="To">
                                </div>
                                <button type="submit" class="btn btn-primary btn-sm mb-2">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm mb-2 ml-2" onclick="resetSearch()">
                                    <i class="fas fa-undo"></i> Reset
                                </button>
                            </form>
                        </div>
                    </div>
                    <div id="customersTableContainer">
                        <div class="text-center py-4"><span class="spinner-border"></span> Loading...</div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<script>
function loadCustomers(page=1) {
    var q = $('#search_query').val();
    var date_from = $('#date_from').val();
    var date_to = $('#date_to').val();
    $('#customersTableContainer').html('<div class="text-center py-4"><span class="spinner-border"></span> Loading...</div>');
    $.get('<?= base_url('admin/wacs/ajaxListOnboardedCustomers') ?>', {
        page: page,
        q: q,
        date_from: date_from,
        date_to: date_to
    }, function(html) {
        $('#customersTableContainer').html(html);
    });
}
$(function(){
    loadCustomers();
    $('#searchForm').on('submit', function(e){
        e.preventDefault();
        loadCustomers();
    });
    $(document).on('click', '.pagination a', function(e){
        e.preventDefault();
        var page = $(this).data('ci-pagination-page') || $(this).attr('data-page') || $(this).text();
        loadCustomers(page);
    });
});
</script>
