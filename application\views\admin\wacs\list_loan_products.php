<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<link rel="stylesheet" href="<?= base_url() ?>assets/plugins/datatables-bs4/css/dataTables.bootstrap4.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.3.1/css/buttons.dataTables.min.css">
<div class="content-wrapper">
	<div class="card">
		<h2>Loan Products </h2>
		<div class="card-body">
			<?php $this->load->view('admin/includes/_messages.php') ?>
			<div class="table-responsive">
				<table class="display table table-hover table-striped table-sm" id="loanProductsTable">
<thead>
	<tr>
		<th>Product ID</th>
		<th>Title</th>
		<th>Description</th>
		<th>Amount Range</th>
		<th>Interest Rate</th>
		<th>Tenure (months)</th>
		<th>Moratorium Period (months)</th>
		<th>Actions</th>
	</tr>
</thead>
					<tbody>
						<?php if (!empty($products)) : ?>
	<?php foreach ($products as $product) : ?>
		<tr>
			<td><?php echo htmlspecialchars($product['product_id']); ?></td>
			<td><?php echo htmlspecialchars($product['title']); ?></td>
			<td><?php echo htmlspecialchars($product['description']); ?></td>
			<td><?php echo htmlspecialchars($product['amount_from']) . ' - ' . htmlspecialchars($product['amount_to']); ?></td>
			<td><?php echo htmlspecialchars($product['interest_rate']); ?></td>
			<td><?php echo htmlspecialchars($product['payback_period']); ?></td>
			<td><?php echo htmlspecialchars($product['moratorium_period']); ?></td>
			<td>
				<button type="button" class="btn btn-sm btn-primary" onclick="openEditModal(<?php echo htmlspecialchars(json_encode($product), ENT_QUOTES, 'UTF-8'); ?>)">Edit</button>
				<?php if ($product['status'] === 'active'): ?>
					<button class="btn btn-sm btn-warning" onclick="confirmAction('<?php echo base_url('admin/wacs/deactivateLoanProduct/' . $product['product_id']); ?>', 'deactivate')">Deactivate</button>
				<?php else: ?>
					<button class="btn btn-sm btn-success" onclick="confirmAction('<?php echo base_url('admin/wacs/activateLoanProduct/' . $product['product_id']); ?>', 'activate')">Activate</button>
				<?php endif; ?>
			</td>
		</tr>
	<?php endforeach; ?>
						<?php else : ?>
							<tr><td colspan="17">No loan products found.</td></tr>
						<?php endif; ?>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>

<!-- Edit Loan Product Modal -->
<div class="modal fade" id="editLoanProductModal" tabindex="-1" role="dialog" aria-labelledby="editLoanProductModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="editLoanProductModalLabel">Edit Loan Product</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body">
				<form id="editLoanProductForm" method="post" class="needs-validation" novalidate>
					<?php echo form_hidden($this->security->get_csrf_token_name(), $this->security->get_csrf_hash()); ?>
					<div class="row">
						<div class="col-md-6">
							<div class="form-group">
								<label for="edit_product_id">Product ID*</label>
								<input type="text" class="form-control" name="product_id" id="edit_product_id" required readonly>
								<div class="invalid-feedback">Product ID is required.</div>
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<label for="edit_title">Title*</label>
								<input type="text" class="form-control" name="title" id="edit_title" required>
								<div class="invalid-feedback">Title is required.</div>
							</div>
						</div>
					</div>
					<div class="form-group">
						<label for="edit_description">Description*</label>
						<textarea class="form-control" name="description" id="edit_description" required></textarea>
						<div class="invalid-feedback">Description is required.</div>
					</div>
					<div class="row">
						<div class="col-md-6">
							<div class="form-group">
								<label for="edit_product_image">Product Image URL</label>
								<input type="url" class="form-control" name="product_image" id="edit_product_image">
								<div class="invalid-feedback">Please provide a valid URL.</div>
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<label for="edit_category">Category*</label>
								<input type="text" class="form-control" name="category" id="edit_category" required>
								<div class="invalid-feedback">Category is required.</div>
							</div>
						</div>
					</div>
					<div class="form-group">
						<label for="edit_feature">Features (comma separated)</label>
						<input type="text" class="form-control" name="feature" id="edit_feature">
					</div>
					<div class="row">
						<div class="col-md-4">
							<div class="form-group">
								<label for="edit_interest_rate">Interest Rate (%)*</label>
								<input type="number" step="0.01" min="0" max="100" class="form-control" name="interest_rate" id="edit_interest_rate" required>
								<div class="invalid-feedback">Interest rate must be between 0 and 100.</div>
							</div>
						</div>
						<div class="col-md-4">
							<div class="form-group">
								<label for="edit_amount_from">Amount From*</label>
								<input type="number" min="0" class="form-control" name="amount_from" id="edit_amount_from" required>
								<div class="invalid-feedback">Minimum amount is required.</div>
							</div>
						</div>
						<div class="col-md-4">
							<div class="form-group">
								<label for="edit_amount_to">Amount To*</label>
								<input type="number" min="0" class="form-control" name="amount_to" id="edit_amount_to" required>
								<div class="invalid-feedback">Maximum amount is required and must be greater than minimum.</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-md-6">
							<div class="form-group">
								<label for="edit_moratorium_period">Moratorium Period (months)*</label>
								<input type="number" min="0" class="form-control" name="moratorium_period" id="edit_moratorium_period" required>
								<div class="invalid-feedback">Moratorium period is required.</div>
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<label for="edit_payback_period">Payback Period (months)*</label>
								<input type="number" min="1" class="form-control" name="payback_period" id="edit_payback_period" required>
								<div class="invalid-feedback">Payback period is required.</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-md-6">
							<div class="form-group">
								<label for="edit_bank_charge_type">Bank Charge Type*</label>
								<select class="form-control" name="bank_charge_type" id="edit_bank_charge_type" required>
									<option value="">Select charge type</option>
									<option value="0">Flat</option>
									<option value="1">Percentage</option>
								</select>
								<div class="invalid-feedback">Charge type is required.</div>
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-group">
								<label for="edit_bank_charge_value">Bank Charge Value*</label>
								<input type="number" min="0" step="0.01" class="form-control" name="bank_charge_value" id="edit_bank_charge_value" required>
								<div class="invalid-feedback">Charge value is required.</div>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
				<button type="button" class="btn btn-primary" onclick="submitEditForm()">Update Loan Product</button>
			</div>
		</div>
	</div>
</div>
<script src="<?= base_url() ?>assets/plugins/datatables/jquery.dataTables.min.js"></script>
<script src="<?= base_url() ?>assets/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.1/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.1/js/buttons.html5.min.js"></script>
<script>
$(document).ready(function () {
	$('#loanProductsTable').DataTable({
		responsive: true,
		pageLength: 25,
		order: [[0, 'desc']],
		dom: 'Bfrtip',
		buttons: [
			'copy', 'csv', 'excel', 'pdf', 'print'
		]
	});

	// Form validation
	var forms = document.querySelectorAll('.needs-validation');
	Array.prototype.slice.call(forms).forEach(function(form) {
		form.addEventListener('submit', function(event) {
			if (!form.checkValidity()) {
				event.preventDefault();
				event.stopPropagation();
			}
			form.classList.add('was-validated');
		}, false);
	});

	// Custom validations
	$('#edit_amount_to').on('change', function() {
		var amountFrom = parseFloat($('#edit_amount_from').val());
		var amountTo = parseFloat($(this).val());
		if (amountTo <= amountFrom) {
			this.setCustomValidity('Amount To must be greater than Amount From');
		} else {
			this.setCustomValidity('');
		}
	});

	$('#edit_interest_rate').on('input', function() {
		var value = parseFloat($(this).val());
		if (value < 0 || value > 100) {
			this.setCustomValidity('Interest rate must be between 0 and 100');
		} else {
			this.setCustomValidity('');
		}
	});

	$('#edit_bank_charge_value').on('input', function() {
		var chargeType = $('#edit_bank_charge_type').val();
		var value = parseFloat($(this).val());
		if (chargeType === '1' && value > 100) {
			this.setCustomValidity('Percentage charge cannot exceed 100%');
		} else {
			this.setCustomValidity('');
		}
	});
});

function openEditModal(product) {
	// Populate modal fields with product data
	$('#edit_product_id').val(product.product_id);
	$('#edit_title').val(product.title);
	$('#edit_description').val(product.description);
	$('#edit_product_image').val(product.product_image || '');
	$('#edit_category').val(product.category);

	// Handle features - convert array to comma-separated string if needed
	var features = '';
	if (Array.isArray(product.feature)) {
		features = product.feature.join(', ');
	} else if (product.feature) {
		features = product.feature;
	}
	$('#edit_feature').val(features);

	$('#edit_interest_rate').val(product.interest_rate);
	$('#edit_amount_from').val(product.amount_from);
	$('#edit_amount_to').val(product.amount_to);
	$('#edit_moratorium_period').val(product.moratorium_period);
	$('#edit_payback_period').val(product.payback_period);
	$('#edit_bank_charge_type').val(product.bank_charge_type);
	$('#edit_bank_charge_value').val(product.bank_charge_value);

	// Set form action
	$('#editLoanProductForm').attr('action', '<?php echo base_url('admin/wacs/updateLoanProduct/'); ?>' + product.product_id);

	// Show modal
	$('#editLoanProductModal').modal('show');
}

function submitEditForm() {
	var form = document.getElementById('editLoanProductForm');
	if (form.checkValidity()) {
		form.submit();
	} else {
		form.classList.add('was-validated');
	}
}

function confirmAction(url, action) {
	if (confirm('Are you sure you want to ' + action + ' this loan product?')) {
		window.location.href = url;
	}
}
</script>
