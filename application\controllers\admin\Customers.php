<?php defined('BASEPATH') or exit('No direct script access allowed');

class Customers extends MY_Controller
{

	public function __construct()
	{

		parent::__construct();
		auth_check(); // check login auth
		$this->rbac->check_module_access();
		define('API_KEY', ($this->settings->remita_islive == 0) ? $this->settings->remita_testapikey : $this->settings->remita_liveapikey);
		define('API_TOKEN', ($this->settings->remita_islive == 0) ? $this->settings->remita_testapitoken : $this->settings->remita_liveapitoken);
		define('MERCHANT_ID', ($this->settings->remita_islive == 0) ? $this->settings->remita_testmerchantid : $this->settings->remita_livemerchantid);
		$uri = ($this->settings->remita_islive == 0) ? $this->settings->remita_testurl : $this->settings->remita_liveurl;
		define('URI', $uri);
		define('GET_SALARY_HISTORY_URL', $uri . '/remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/salary/history/ph');
		define('MANDATE_PAYMENT_HISTORY', $uri . '/remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/loan/payment/history');
		define('DISBURSEMENT_NOTIFICATION_URL', $uri . '/remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/post/loan');
		define('GET_SALARY_HISTORY_URL_NUBAN', $uri . '/remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/salary/history/provideCustomerDetails');

	}



	public function index()
	{
		$this->rbac->check_operation_access();
		// Load views if not exporting
		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/index');
		$this->load->view('admin/includes/_footer');
	}

	/**
	 * Helper function to get remita status as a string with badges.
	 */
	private function getRemitaStatus($onremitaStatus, $noOfSearches)
	{
		$status = '';

		switch ($onremitaStatus) {
			case 1:
				$status = '<span class="badge badge-success">on remita</span>';
				break;
			case 2:
				$status = '<span class="badge badge-info">suspended</span>';
				break;
			case 3:
				$status = '<span class="badge badge-secondary">unknown</span>';
				break;
			default:
				$status = '<span class="badge badge-danger">not on remita</span>';
				break;
		}

		$status .= '<br><span class="badge badge-warning">' . $noOfSearches . '<i class="fa fa-search"></i></span>';

		return $status;
	}


	public function ninLookup(){
		
	    $this->rbac->check_operation_access();
	    
	    // Initialize response array for AJAX
	    $response = [
	        'status' => false,
	        'message' => '',
	        'data' => null
	    ];
	    
	    try {
	        // Get NIN from request
	        $nin = $this->input->post('nin');
	        
	        if (empty($nin)) {
	            throw new Exception('NIN is required');
	        }
	        
	        // Check if NIN already exists in database
	        $existing_nin = $this->db->get_where('nin_data', ['nin' => $nin])->row();
	        
	        // Check wallet balance before proceeding
	        $wallet = $this->db->get_where('wallet_transactions', ['id' => 1])->row();
	        if (80 > wallet_balance($this->session->userdata('id'))) {
	            $response = [
	                'status' => false,
	                'message' => 'Insufficient wallet balance for NIN lookup',
	                'data' => ''
	            ];
	            
	            // Set flash message for non-AJAX requests
	            $this->session->set_flashdata('error',  'Insufficient wallet balance for NIN lookup');
	            throw new Exception('Insufficient wallet balance for NIN lookup');
	        }
	        
	        // Prepare API request to Mono
	        $url = 'https://api.withmono.com/v3/lookup/nin';
	        $headers = [
	            'Content-Type: application/json',
	            'Accept: application/json',
	            // 'mono-sec-key: ' . $this->settings->mono_api_key
	            'mono-sec-key: ' . 'live_sk_jqhmwfxidborh5tlbz8s'
	        ];
	        
	        $data = json_encode(['nin' => $nin]);
	        
	        // Initialize cURL session
	        $ch = curl_init($url);
	        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	        curl_setopt($ch, CURLOPT_POST, true);
	        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
	        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
	        
	        // Execute cURL request
	        $response_data = curl_exec($ch);
	        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	        
	        // Check for cURL errors
	        if (curl_errno($ch)) {
	            throw new Exception('cURL Error: ' . curl_error($ch));
	        }
	        
	        curl_close($ch);
	        
	        // Parse response
	        $result = json_decode($response_data);
	        
	        // Check if request was successful
	        if ($http_code != 200 || empty($result) || !isset($result->data)) {
	            $error_message = isset($result->message) ? $result->message : 'Failed to verify NIN. Please try again.';
	            throw new Exception($error_message);
	        }
	        
	        $wallet_transaction_data = [
	            'reference' => generate_unique_reference(10),
	            'creditordebit' => 'dr',
	            'amount' => 80,
	            'channel' => 'nin-lookup',
	            'balanceafter' => balance_after($this->session->userdata('id'), 'dr', 80),
	            'narration' => 'NIN Lookup for ' . $nin,
	            'datecreated' => date('Y-m-d H:i:s'),
	            'createdby' => $this->session->userdata('id'),
	            'userid' => $this->session->userdata('id')
	        ];

	        $this->Common_model->save($wallet_transaction_data, null, 'wallet_transactions');
	        
	        // Map API response to database fields
	        $nin_data = [
	            'nin' => $nin,
	            'firstname' => isset($result->data->firstname) ? $result->data->firstname : null,
	            'middlename' => isset($result->data->middlename) ? $result->data->middlename : null,
	            'surname' => isset($result->data->surname) ? $result->data->surname : null,
	            'birthdate' => isset($result->data->birthdate) ? date('Y-m-d', strtotime($result->data->birthdate)) : null,
	            'gender' => isset($result->data->gender) ? $result->data->gender : null,
	            'telephoneno' => isset($result->data->telephoneno) ? $result->data->telephoneno : null,
	            'email' => isset($result->data->email) ? $result->data->email : null,
	            'photo' => isset($result->data->photo) ? $result->data->photo : null,
	            'signature' => isset($result->data->signature) ? $result->data->signature : null,
	            'birthcountry' => isset($result->data->birthcountry) ? $result->data->birthcountry : null,
	            'birthlga' => isset($result->data->birthlga) ? $result->data->birthlga : null,
	            'birthstate' => isset($result->data->birthstate) ? $result->data->birthstate : null,
	            'educationallevel' => isset($result->data->educationallevel) ? $result->data->educationallevel : null,
	            'employmentstatus' => isset($result->data->employmentstatus) ? $result->data->employmentstatus : null,
	            'heigth' => isset($result->data->heigth) ? $result->data->heigth : null,
	            'maritalstatus' => isset($result->data->maritalstatus) ? $result->data->maritalstatus : null,
	            'nok_address1' => isset($result->data->nok_address1) ? $result->data->nok_address1 : null,
	            'nok_address2' => isset($result->data->nok_address2) ? $result->data->nok_address2 : null,
	            'nok_firstname' => isset($result->data->nok_firstname) ? $result->data->nok_firstname : null,
	            'nok_lga' => isset($result->data->nok_lga) ? $result->data->nok_lga : null,
	            'nok_middlename' => isset($result->data->nok_middlename) ? $result->data->nok_middlename : null,
	            'nok_postalcode' => isset($result->data->nok_postalcode) ? $result->data->nok_postalcode : null,
	            'nok_state' => isset($result->data->nok_state) ? $result->data->nok_state : null,
	            'nok_surname' => isset($result->data->nok_surname) ? $result->data->nok_surname : null,
	            'nok_town' => isset($result->data->nok_town) ? $result->data->nok_town : null,
	            'ospokenlang' => isset($result->data->ospokenlang) ? $result->data->ospokenlang : null,
	            'pfirstname' => isset($result->data->pfirstname) ? $result->data->pfirstname : null,
	            'pmiddlename' => isset($result->data->pmiddlename) ? $result->data->pmiddlename : null,
	            'profession' => isset($result->data->profession) ? $result->data->profession : null,
	            'psurname' => isset($result->data->psurname) ? $result->data->psurname : null,
	            'religion' => isset($result->data->religion) ? $result->data->religion : null,
	            'residence_address' => isset($result->data->residence_address) ? $result->data->residence_address : null,
	            'residence_lga' => isset($result->data->residence_lga) ? $result->data->residence_lga : null,
	            'residence_state' => isset($result->data->residence_state) ? $result->data->residence_state : null,
	            'residence_town' => isset($result->data->residence_town) ? $result->data->residence_town : null,
	            'residencestatus' => isset($result->data->residencestatus) ? $result->data->residencestatus : null,
	            'self_origin_lga' => isset($result->data->self_origin_lga) ? $result->data->self_origin_lga : null,
	            'self_origin_place' => isset($result->data->self_origin_place) ? $result->data->self_origin_place : null,
	            'self_origin_state' => isset($result->data->self_origin_state) ? $result->data->self_origin_state : null,
	            'spoken_language' => isset($result->data->spoken_language) ? $result->data->spoken_language : null,
	            'title' => isset($result->data->title) ? $result->data->title : null,
	            'userid' => isset($result->data->userid) ? $result->data->userid : null,
	            'vnin' => isset($result->data->vnin) ? $result->data->vnin : null,
	            'central_id' => isset($result->data->central_id) ? $result->data->central_id : null,
	            'tracking_id' => isset($result->data->tracking_id) ? $result->data->tracking_id : null,
	            'api_response' => $response_data // Store the complete API response
	        ];
	        
	        // Save or update NIN data
	        if ($existing_nin) {
	            // Update existing record
	            $this->db->where('nin', $nin);
	            $this->db->update('nin_data', $nin_data);
	            $success_message = 'NIN data updated successfully';
	        } else {
	            // Insert new record
	            $this->db->insert('nin_data', $nin_data);
	            $success_message = 'NIN data saved successfully';
	        }
	        
	        // Generate PDF for the NIN data
	        $pdf_url = $this->generate_nin_pdf($nin);
	        
	        // Get the saved/updated NIN data for response
	        $saved_nin_data = $this->db->get_where('nin_data', ['nin' => $nin])->row();
	        
	        // Set success response
	        $response = [
	            'status' => true,
	            'message' => $success_message,
	            'pdf_url' => $pdf_url,
	            'data' => $saved_nin_data
	        ];
	        
	        // Set flash message for non-AJAX requests
	        $this->session->set_flashdata('success', $success_message);
	        $this->session->set_flashdata('pdf_url', $pdf_url);
	        
	    } catch (Exception $e) {
	        // Log the error
	        log_message('error', 'NIN Lookup Error: ' . $e->getMessage());
	        
	        // Set error response
	        $response = [
	            'status' => false,
	            'message' => $e->getMessage(),
	            'data' => null
	        ];
	        
	        // Set flash message for non-AJAX requests
	        $this->session->set_flashdata('error', $e->getMessage());
	    }
	    
	    // Check if this is an AJAX request
	    if ($this->input->is_ajax_request()) {
	        // Send JSON response
	        echo json_encode($response);
	        return;
	    }
	    
	    // For non-AJAX requests, redirect back
	    redirect($_SERVER['HTTP_REFERER']);
	}

	/**
	 * Generate PDF for NIN data
	 * 
	 * @param string $nin NIN number
	 * @return string URL to the generated PDF
	 */
	private function generate_nin_pdf($nin)
	{
	    // Get NIN data
	    $nin_data = $this->db->get_where('nin_data', ['nin' => $nin])->row();
	    
	    if (!$nin_data) {
	        return '';
	    }
	    
	    // Load TCPDF library
	    $this->load->library('pdf');
	    
	    // Create new PDF document
	    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
	    
	    // Set document information
	    $pdf->SetCreator(PDF_CREATOR);
	    $pdf->SetAuthor('CMF Bremita');
	    $pdf->SetTitle('NIN Verification - ' . $nin_data->nin);
	    $pdf->SetSubject('NIN Verification Report');
	    $pdf->SetKeywords('NIN, Verification, Identity');
	    
	    // Set default header data
	    $pdf->SetHeaderData(PDF_HEADER_LOGO, PDF_HEADER_LOGO_WIDTH, 'NIN Verification Report', 'Generated on: ' . date('Y-m-d H:i:s'));
	    
	    // Set header and footer fonts
	    $pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
	    $pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));
	    
	    // Set default monospaced font
	    $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
	    
	    // Set margins
	    $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
	    $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
	    $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);
	    
	    // Set auto page breaks
	    $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
	    
	    // Set image scale factor
	    $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
	    
	    // Add a page
	    $pdf->AddPage();
	    
	    // Set font
	    $pdf->SetFont('helvetica', '', 10);
	    
	    // Build HTML content for PDF
	    $html = '<h1 style="text-align:center;">NIN Verification Report</h1>';
	    $html .= '<div style="text-align:center;margin-bottom:20px;"><strong>NIN: ' . $nin_data->nin . '</strong></div>';
	    
	    // Add photo if available
	    if (!empty($nin_data->photo)) {
	        $html .= '<div style="text-align:center;margin-bottom:20px;">';
	        $html .= '<img src="data:image/jpeg;base64,' . $nin_data->photo . '" style="width:100px;height:auto;">';
	        $html .= '</div>';
	    }
	    
	    // Personal Information
	    $html .= '<h2>Personal Information</h2>';
	    $html .= '<table border="1" cellpadding="5">';
	    $html .= '<tr><th width="30%">Field</th><th width="70%">Value</th></tr>';
	    $html .= '<tr><td>Title</td><td>' . $nin_data->title . '</td></tr>';
	    $html .= '<tr><td>Full Name</td><td>' . $nin_data->firstname . ' ' . $nin_data->middlename . ' ' . $nin_data->surname . '</td></tr>';
	    $html .= '<tr><td>Gender</td><td>' . $nin_data->gender . '</td></tr>';
	    $html .= '<tr><td>Date of Birth</td><td>' . $nin_data->birthdate . '</td></tr>';
	    $html .= '<tr><td>Phone Number</td><td>' . $nin_data->telephoneno . '</td></tr>';
	    $html .= '<tr><td>Email</td><td>' . $nin_data->email . '</td></tr>';
	    $html .= '<tr><td>Marital Status</td><td>' . $nin_data->maritalstatus . '</td></tr>';
	    $html .= '<tr><td>Profession</td><td>' . $nin_data->profession . '</td></tr>';
	    $html .= '<tr><td>Religion</td><td>' . $nin_data->religion . '</td></tr>';
	    $html .= '</table>';
	    
	    // Birth Information
	    $html .= '<h2>Birth Information</h2>';
	    $html .= '<table border="1" cellpadding="5">';
	    $html .= '<tr><th width="30%">Field</th><th width="70%">Value</th></tr>';
	    $html .= '<tr><td>Birth Country</td><td>' . $nin_data->birthcountry . '</td></tr>';
	    $html .= '<tr><td>Birth State</td><td>' . $nin_data->birthstate . '</td></tr>';
	    $html .= '<tr><td>Birth LGA</td><td>' . $nin_data->birthlga . '</td></tr>';
	    $html .= '</table>';
	    
	    // Residence Information
	    $html .= '<h2>Residence Information</h2>';
	    $html .= '<table border="1" cellpadding="5">';
	    $html .= '<tr><th width="30%">Field</th><th width="70%">Value</th></tr>';
	    $html .= '<tr><td>Address</td><td>' . $nin_data->residence_address . '</td></tr>';
	    $html .= '<tr><td>State</td><td>' . $nin_data->residence_state . '</td></tr>';
	    $html .= '<tr><td>LGA</td><td>' . $nin_data->residence_lga . '</td></tr>';
	    $html .= '<tr><td>Town</td><td>' . $nin_data->residence_town . '</td></tr>';
	    $html .= '<tr><td>Residence Status</td><td>' . $nin_data->residencestatus . '</td></tr>';
	    $html .= '</table>';
	    
	    // Next of Kin Information
	    $html .= '<h2>Next of Kin Information</h2>';
	    $html .= '<table border="1" cellpadding="5">';
	    $html .= '<tr><th width="30%">Field</th><th width="70%">Value</th></tr>';
	    $html .= '<tr><td>Full Name</td><td>' . $nin_data->nok_firstname . ' ' . $nin_data->nok_middlename . ' ' . $nin_data->nok_surname . '</td></tr>';
	    $html .= '<tr><td>Address</td><td>' . $nin_data->nok_address1 . ' ' . $nin_data->nok_address2 . '</td></tr>';
	    $html .= '<tr><td>State</td><td>' . $nin_data->nok_state . '</td></tr>';
	    $html .= '<tr><td>LGA</td><td>' . $nin_data->nok_lga . '</td></tr>';
	    $html .= '<tr><td>Town</td><td>' . $nin_data->nok_town . '</td></tr>';
	    $html .= '<tr><td>Postal Code</td><td>' . $nin_data->nok_postalcode . '</td></tr>';
	    $html .= '</table>';
	    
	    // Origin Information
	    $html .= '<h2>Origin Information</h2>';
	    $html .= '<table border="1" cellpadding="5">';
	    $html .= '<tr><th width="30%">Field</th><th width="70%">Value</th></tr>';
	    $html .= '<tr><td>State of Origin</td><td>' . $nin_data->self_origin_state . '</td></tr>';
	    $html .= '<tr><td>LGA of Origin</td><td>' . $nin_data->self_origin_lga . '</td></tr>';
	    $html .= '<tr><td>Place of Origin</td><td>' . $nin_data->self_origin_place . '</td></tr>';
	    $html .= '</table>';
	    
	    // Other Information
	    $html .= '<h2>Other Information</h2>';
	    $html .= '<table border="1" cellpadding="5">';
	    $html .= '<tr><th width="30%">Field</th><th width="70%">Value</th></tr>';
	    $html .= '<tr><td>Educational Level</td><td>' . $nin_data->educationallevel . '</td></tr>';
	    $html .= '<tr><td>Employment Status</td><td>' . $nin_data->employmentstatus . '</td></tr>';
	    $html .= '<tr><td>Height</td><td>' . $nin_data->heigth . '</td></tr>';
	    $html .= '<tr><td>Spoken Language</td><td>' . $nin_data->spoken_language . '</td></tr>';
	    $html .= '<tr><td>Other Spoken Language</td><td>' . $nin_data->ospokenlang . '</td></tr>';
	    $html .= '</table>';
	    
	    // Verification Information
	    $html .= '<h2>Verification Information</h2>';
	    $html .= '<table border="1" cellpadding="5">';
	    $html .= '<tr><th width="30%">Field</th><th width="70%">Value</th></tr>';
	    $html .= '<tr><td>NIN</td><td>' . $nin_data->nin . '</td></tr>';
	    $html .= '<tr><td>vNIN</td><td>' . $nin_data->vnin . '</td></tr>';
	    $html .= '<tr><td>Central ID</td><td>' . $nin_data->central_id . '</td></tr>';
	    $html .= '<tr><td>Tracking ID</td><td>' . $nin_data->tracking_id . '</td></tr>';
	    $html .= '<tr><td>User ID</td><td>' . $nin_data->userid . '</td></tr>';
	    $html .= '</table>';
	    
	    // Add signature if available
	    if (!empty($nin_data->signature)) {
	        $html .= '<div style="text-align:center;margin-top:20px;">';
	        $html .= '<p><strong>Signature:</strong></p>';
	        $html .= '<img src="data:image/jpeg;base64,' . $nin_data->signature . '" style="width:200px;height:auto;">';
	        $html .= '</div>';
	    }
	    
	    // Add verification timestamp
	    $html .= '<div style="text-align:center;margin-top:30px;">';
	    $html .= '<p>This document was generated on ' . date('Y-m-d H:i:s') . '</p>';
	    $html .= '<p>Verification performed by CMF Bremita</p>';
	    $html .= '</div>';
	    
	    // Output HTML content to PDF
	    $pdf->writeHTML($html, true, false, true, false, '');
	    
	    // Create directory if it doesn't exist
	    $upload_dir = 'uploads/nin_reports/';
	    if (!is_dir($upload_dir)) {
	        mkdir($upload_dir, 0777, true);
	    }
	    
	    // Generate unique filename
	    $filename = 'nin_verification_' . $nin_data->nin . '_' . date('YmdHis') . '.pdf';
	    $filepath = $upload_dir . $filename;
	    
	    // Save PDF to file
	    $pdf->Output($filepath, 'F');
	    
	    // Return URL to the PDF file
	    return base_url($filepath);
	}


	public function searchlist()
	{
		$this->rbac->check_operation_access();
		$params = [];
		$search = null;

		$search = $this->input->get('search');

		$limit = 50; // Number of records per page
		$offset = $this->input->get('offset') ? $this->input->get('offset') : 0; // Offset for pagination
		$customerData = $this->Customer_model->getAllCustomerSearch($params, $limit, $offset, $search);
		$data = array(
			'records' => $customerData,
			'total_rows' => countwhere('customers', ['onremita' => 1]),
			'limit' => $limit,
			'offset' => $offset,
			'search' => $search

		);

		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/searchlist', $data);
		$this->load->view('admin/includes/_footer');
	}

	public function get_customers_data()
	{
		$params['start'] = 1;
		$params['length'] = 20;
		$params['search'] = [];
		$params['order'] = 0;
		$data = $this->Common_model->get_json_data('customers', $params); // Call the method from your model to retrieve data from the database
		echo json_encode($data);
	}

	//anonymous access
	public function getAllCustomers($params = null)
	{
		$params['onremita'] = 1;
		$records['data'] = $this->Customer_model->getAllCustomers($params);
		$data = array();
		$i = 0;
		$office = '';
		foreach ($records['data'] as $row) {
			//$status = ($row['is_active'] == 1)? 'checked': '';

			$onremita = '';
			$no_of_searchs = countwhere('customer_successful_search', ['customerid' => $row['remitacustomerid']]);
			if ($row['onremita'] == 1) {
				$onremita = '<span class="badge badge-success">on remita</span>';
			} elseif ($row['onremita'] == 2) {
				$onremita = '<span class="badge badge-info">suspended</span>';
			} elseif ($row['onremita'] == 3) {
				$onremita = '<span class="badge badge-secondary">unknown</span>';
			} else {
				$onremita = '<span class="badge badge-danger">not on remita</span>';
			}

			$onremita .= '<br><span class="badge badge-warning">' . $no_of_searchs . '<i class="fa fa-search"></i></span>';
			$office = empty($row['office']) ? '' : '<small>(' . $row['office'] . ')</small>';
			if (!empty(getbyid($row['office'], 'mdas'))) {
				$mda = getbyid($row['office'], 'mdas');
				$department = !empty($mda->department) ? 'Dep: ' . $mda->department : '';
				$agency = !empty($mda->ageny) ? 'Agen: ' . $mda->agency : '';
				$ministry = !empty($mda->ministry) ? 'Min: ' . $mda->ministry : '';
				$office = '(' . $ministry . ' ' . $department . ' ' . $agency . ')';
			}

			$salary = !empty(getby(['phone' => $row['phone']], 'customer_salary_loan')) ? getby(['phone' => $row['phone']], 'customer_salary_loan') : null;
			$sal = !empty($salary) ? json_decode($salary->salary) : null;
			$loans = !empty($salary->loan) ? sizeof(json_decode($salary->loan)) : null;


			$data[] = array(
				$row['id'],
				'<a href="' . base_url('admin/customers/salary_loan_details/' . $row['phone']) . '" class="text-dark" title="View Customer Profile" >' . $row['fullname'] . '</a><br><small class="text-primary">' . $row['remitacustomerid'] . ' </small>',
				'<a href="' . base_url('admin/customers/search/' . $row['phone']) . '" title="click to recheck remita" class="text-dark">' . $row['phone'] . '</a>',
				$row['organization'] . '<br>' . $office,
				!empty($sal) ? formatMoney($sal[0]->amount) : '<span class="badge badge-danger"> no data </span>',
				!empty($loans) ? $loans : '<span class="badge bg-success">none</span>',
				$onremita,
				formatDate($row['lastchecked']),
				'<input type="checkbox" name="" class="flat-red" id=customer' . $row['id'] . ' >'
			);

			$csvData[] = array(
				$row['fullname'],
				$row['phone'],
				$row['bvn'],
				$row['organization'],
				$row['office'],
				!empty($sal) ? formatMoney($sal[0]->amount) : 'no data',
				!empty($loans) ? $loans : 'none',
				strip_tags($onremita),
				formatDate($row['lastchecked']),
			);
		}
		$records['data'] = $data;
		if (!empty($_GET['export']) && $_GET['export'] == 'csv') {
			ob_clean();
			$filename = 'remita_customers_' . strtotime(date('Y-m-d H:i:s')) . '.csv';
			header("Content-Description: File Transfer");
			header("Content-Disposition: attachment; filename=$filename");
			header("Content-Type: application/csv; ");
			// file creation 
			$file = fopen('php://output', 'w');

			$header = array("Name", "Phone", "BVN", "Organization", "Office", "Salary", "Total Loans", "On Remita", "Last Checked On");
			fputcsv($file, $header);
			foreach ($csvData as $key => $line) {
				fputcsv($file, $line);
			}
			fclose($file);
			exit;
		}
		echo json_encode($records);
	}

	public function loans()
	{
		$this->rbac->check_operation_access();
		$filter = [];

		if (!empty($_REQUEST['status'])) {
			$filter['status'] = $_REQUEST['status'];
		}

		if (!empty($_GET['export']) && $_GET['export'] == 'csv') {
			foreach (getalldata('loans') as $row) {

				$customer_successful_search = empty(getby(['customerid' => $row->customerid], 'customer_successful_search')) ? false : getby(['customerid' => $row->customerid], 'customer_successful_search');

				$customerData = !empty($customer_successful_search->response) ? json_decode($customer_successful_search->response)->data : null;
				$csvData[] = array(
					$row->id,
					$row->mandateref,
					$row->status,
					$customerData->customerName,
					$customerData->bvn,
					$customerData->accountNumber,
					!empty(getby(['code' => $customerData->bankCode], 'banks')) ? getby(['code' => $customerData->bankCode], 'banks')->name : 'unknown',
					$row->loanamount,
					$row->numberofrepayments,
					$row->collectionamount,
					formatDate($row->datecreated),
					''
				);

			}

			ob_clean();
			$filename = 'loans_' . strtotime(date('Y-m-d H:i:s')) . '.csv';
			header("Content-Description: File Transfer");
			header("Content-Disposition: attachment; filename=$filename");
			header("Content-Type: application/csv; ");
			$file = fopen('php://output', 'w');

			$header = array("id", "Mandate Ref", "Status", "Name", "BVN", "Salary Acct No", "Salary Bank", "Principal", "Tenure", "Repayment", "Loan date");
			fputcsv($file, $header);
			foreach ($csvData as $key => $line) {
				fputcsv($file, $line);
			}
			fclose($file);
			exit;
		}
		$view_data['loans'] = getalldata('loans', $filter, 'id', 'desc');
		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/loans', $view_data);
		$this->load->view('admin/includes/_footer');
	}

	public function loandetails($id = 0)
	{
		$this->rbac->check_operation_access();
		$data['loan'] = getbyid($id, 'loans');
		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/searchlist', $data);
		$this->load->view('admin/includes/_footer');
	}

	public function fetch_loans()
	{
		$columns = ['id', 'remitadetails', 'customer', 'creationdeatils', 'principal', 'tenor', 'interest', 'repayment', 'status', 'action'];
		$limit = $this->input->post('length');
		$start = $this->input->post('start');
		$statusFilter = !empty($this->input->post('statusFilter')) ? $this->input->post('statusFilter') : 'created';
		$order = isset($this->input->post('order')[0]['column']) ? $columns[$this->input->post('order')[0]['column']] : 'id';
		$dir = isset($this->input->post('order')[0]['dir']) ? $this->input->post('order')[0]['dir'] : 'desc';

		$totalData = datatableCountAll('loans');
		$recordsFiltered = datatableCountFilter('loans', 'status', $statusFilter);

		$search = $this->input->post('search')['value'];
		if (empty($search)) {
			$loans = allDatatableData('loans', $limit, $start, $order, $dir, 'status', $statusFilter, null, null, null);
		} else {
			// Search by account number in nested customer data
			$loans = $this->searchByCustomerFields($search, $limit, $start, $order, $dir, $statusFilter);

			$recordsFiltered = count($loans); // Update filtered count based on search results
			//$customers =  datatableSearch('loans',$limit,$start,$search,$order,$dir);
			//$recordsFiltered = datatableSearchCount('customers',$search);
		}
		$data = array();
		if (!empty($loans)) {
			foreach ($loans as $loan) {
				$customer_successful_search = empty(getby(['customerid' => $loan->customerid], 'customer_successful_search')) ? false : getby(['customerid' => $loan->customerid], 'customer_successful_search');
				$customerData = !empty($customer_successful_search->response) ? json_decode($customer_successful_search->response)->data : null;


				$actions = '<div class="btn-group">
					                <button type="button" class="btn btn-default btn-sm" data-toggle="dropdown">
					                    <i class="fa fa-arrow-circle-right"></i>
					                </button>
					                <div class="dropdown-menu" role="menu">';

				$actions .= '<a class="dropdown-item" href="' . base_url('admin/customers/loan_offer_letter/' . $loan->id) . '">Offer Letter</a>';

				if (empty($loan->mandateref) && $loan->status == 'created') {
					$actions .= '<div class="dropdown-divider"></div>';
					$actions .= '<a class="dropdown-item" href="' . base_url('admin/customers/approveloan/' . $loan->id) . '" data-toggle="ajax-modal">Approval</a>';

					if ($loan->status != 'closed') {
						$actions .= '<div class="dropdown-divider"></div>';
						$actions .= '<a class="dropdown-item" href="' . base_url('admin/customers/closeloan/' . $loan->id) . '" onclick="return confirm(\'Are you sure you want to close this loan?\')">Close</a>';
					}
				} else {
					if ($loan->status != "closed" && $loan->status != "declined") {
						$actions .= '<div class="dropdown-divider"></div>';
						$actions .= '<a class="dropdown-item" href="' . base_url('admin/customers/stoploan/' . $loan->id) . '" data-toggle="ajax-modal">Terminate</a>';
					}
				}

				$actions .= '<div class="dropdown-divider"></div>';

				if ($loan->offerconsent == 0 && $loan->status != "closed" && $loan->status != "declined") {
					$actions .= '<a class="dropdown-item" href="' . base_url('frontend/resend_offer/' . $loan->id) . '">Send Offer (' . $loan->regeneratetimes . ')</a><div class="dropdown-divider"></div>';
				}
				$actions .= '<a class="dropdown-item" href="' . base_url('admin/customers/salary_loan_details/?cid=' . $loan->customerid) . '">Salary Details</a>';

				if (!empty($loan->mandateref)) {
					$actions .= '<div class="dropdown-divider"></div>';
					$actions .= '<a class="dropdown-item" href="' . base_url('admin/customers/mandatepaymenthistory/?cid=' . $loan->customerid . '&man=' . $loan->mandateref . '&auth=' . $loan->authcode) . '">Check Repayment</a>';
				}

				$actions .= '<div class="dropdown-divider"></div>';
				$actions .= '<a class="dropdown-item" href="' . base_url('admin/customers/editcustomer/' . $loan->id) . '" data-toggle="ajax-modal">Edit Customer</a>';

				$actions .= '</div></div>';

				$details = '<span class="badge badge-info">' . $loan->bookedwith . '</span><small>';
				$details .= '<span class="d-block"><b>Remita Id:</b> ' . $loan->customerid . '</span>';
				$details .= '<span class="d-block"><b>Man:</b> ' . (empty($loan->mandateref) ? 'none' : $loan->mandateref . ' <span class="fa fa-check-circle text-success"></span>') . '</span>';
				$details .= '<span class="d-block"><b>Auth:</b> ' . $loan->authcode . '</span>';

				$details .= '</small>';

				$info = '<div class="text-primary">';
				if (!empty($customerData->customerName)) {
					$info .= ucwords(strtolower($customerData->customerName));
				} else {
					$info .= '<a class="text-danger dropdown-item" href="' . base_url('admin/customers/editcustomer/' . $loan->id) . '" data-toggle="ajax-modal">no name</a>';
				}

				$info .= '</div>
					              <small>
					                  <b>Acc No:</b> ' . $customerData->accountNumber . ' <br>
					                  <b>Bank:</b> ' . (!empty(getby(['code' => $customerData->bankCode], 'banks')) ? getby(['code' => $customerData->bankCode], 'banks')->name : 'unknown') . ' <br>
					                  <b>Phone:</b> ' . $loan->phone . '
					              </small>';
				$approval = !empty($loan->dateapproved) ? formatDate($loan->dateapproved) : '<span class="badge badge-danger">pending<span>';
				$otpExpired = (!empty($loan->otp_expires_at) && strtotime($loan->otp_expires_at) < time()) ? 'Expired' : null;
				$creationinfo = '<small><b>Created: </b>' . formatDate($loan->datecreated) . ' by <span class="badge badge-primary">' . nameofuser($loan->createdby) . '</span></small>';
				$creationinfo .= '<br><small><b>Approved: </b>' . $approval . '</small>';
				$monthlyRepayment = ($loan->rate / 100) * $loan->loanamount;
				$totalRepayment = $monthlyRepayment * $loan->numberofrepayments;

				$interestinfo = '<small><b>Mnts:</b> ' . formatMoney($monthlyRepayment) . '</small><br>';
				$interestinfo .= '<small><b>Total:</b> ' . formatMoney($totalRepayment) . '</small><br>';
				$interestinfo .= '<small><b>Rate:</b> ' . round($loan->rate, 2) . '%</small>';

				$repaymentInfo = '<small>' .
					'<b>Mnts:</b> ' . formatMoney($loan->collectionamount) . '<br>' .
					'<b>Total:</b> ' . formatMoney($loan->totalcollectionamount) .
					'</small>';

				$badgeClass = '';
				$title = '';
				$date = '';
				$status = $loan->status;

				switch ($status) {
					case 'created':
						$badgeClass = 'badge-primary';
						$date = formatDate($loan->datecreated);
						break;
					case 'approved':
						$badgeClass = 'badge-warning';
						$title = $loan->approvalcomment;
						$date = formatDate($loan->dateapproved);
						break;
					case 'disbursed':
						$badgeClass = 'badge-success';
						$title = $loan->approvalcomment;
						$date = formatDate($loan->datedisbursed);
						break;
					case 'closed':
						$badgeClass = 'badge-danger';
						$title = $loan->terminationcomment;
						$date = formatDate($loan->dateterminated);
						break;
					case 'declined':
						$badgeClass = 'badge-warning';
						$title = $loan->approvalcomment;
						$date = formatDate($loan->dateapproved);
						break;
				}
				$tokenGenerationDate = !empty($loan->tokengenerationdate) ? formatDate($loan->tokengenerationdate) : 'no token';
				$statusInfo = '<div>' .
					'<span class="badge ' . $badgeClass . '" title="' . $title . '">' . $status . '</span>' .
					'<small class="d-block">' . formatDate($date) . '</small>' .
					'</div>';
				$otpPendingStatus = !empty($otpExpired) ? 'OTP Expired' : 'pending';

				$customerConsent = !empty($loan->offerconsent) && $loan->offerconsent == 1 ? '<span class="badge badge-success">Accepted on </span> ' . formatDate($loan->consentdate) : '<span class="badge badge-danger" title="generated on ' . $tokenGenerationDate . '">' . $otpPendingStatus . '<span>';
				$consentDetails = !empty($loan->mandateref) ? '<br> <small><b>Offer Consent:</b> ' . $customerConsent . ' </b></small>' : '';
				$nestedData['action'] = $actions;
				$nestedData['id'] = $loan->id;

				$nestedData['remitadetails'] = $details;
				$nestedData['customer'] = $info;
				$nestedData['creationdeatils'] = $creationinfo . $consentDetails;
				$nestedData['principal'] = formatMoney($loan->loanamount);
				$nestedData['tenor'] = $loan->numberofrepayments;
				$nestedData['interest'] = $interestinfo;
				$nestedData['repayment'] = $repaymentInfo;
				$nestedData['status'] = $statusInfo;

				$data[] = $nestedData;

			}
		}

		$json_data = array(
			"draw" => intval($this->input->post('draw')),
			"recordsTotal" => intval($totalData),
			"recordsFiltered" => intval($recordsFiltered),
			"data" => $data
		);

		echo json_encode($json_data);
	}

	private function searchByCustomerFields($searchTerm, $limit, $start, $order, $dir, $statusFilter)
	{
		// Step 1: Retrieve loans with the existing filters (apply limit and order)
		$loans = allDatatableData('loans', null, $start, $order, $dir, 'status', $statusFilter, null, null, null);

		if (empty($loans)) {
			return [];
		}

		// Step 2: Extract all customer IDs from the loans
		$loanCustomerIds = array_column($loans, 'customerid');

		// Step 3: Fetch all customer_successful_search records in a single query
		$this->db->select('customerid, response')
			->from('customer_successful_search')
			->where_in('customerid', $loanCustomerIds);
		$customerSearchResults = $this->db->get()->result_array();

		// Map customerid to their response for faster lookup
		$customerDataMap = [];
		foreach ($customerSearchResults as $record) {
			if (!empty($record['response'])) {
				$customerDataMap[$record['customerid']] = json_decode($record['response'])->data;
			}
		}

		// Step 4: Match loans with customer data
		$result = [];
		foreach ($loans as $loan) {
			if (isset($customerDataMap[$loan->customerid])) {
				$customerData = $customerDataMap[$loan->customerid];

				// Check if any field matches the search term
				$accountMatch = !empty($customerData->accountNumber) && strpos($customerData->accountNumber, $searchTerm) !== false;
				$nameMatch = !empty($customerData->customerName) && stripos($customerData->customerName, $searchTerm) !== false;
				$idMatch = !empty($customerData->customerId) && strpos($customerData->customerId, $searchTerm) !== false;

				if ($accountMatch || $nameMatch || $idMatch) {
					$result[] = $loan;
				}
			}
		}

		return $result;
	}



	public function collection_notification($phone = '')
	{
		$this->rbac->check_operation_access();
		// $this->functions->sendGetRequest($)
		$data['customer_searchs'] = getalldata('customer_successful_search', null, 'id', 'DESC', 0, 4);
		$data['phone'] = $phone;
		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/search', $data);
		$this->load->view('admin/includes/_footer');
	}

	public function collectionlog($phone = '')
	{
		$this->rbac->check_operation_access();
		$data['collections'] = getalldata('drfcollections', null, 'id', 'DESC');
		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/collectionlog', $data);
		$this->load->view('admin/includes/_footer');
	}

	public function loan_offer_letter($loanid = '')
	{
		$this->rbac->check_operation_access();
		$data['loan'] = empty(getbyid($loanid, 'loans')) ? show_404() : getbyid($loanid, 'loans');
		$data['customer_successful_search'] = empty(getby(['customerid' => $data['loan']->customerid], 'customer_successful_search')) ? false : getby(['customerid' => $data['loan']->customerid], 'customer_successful_search');
		$data['customerData'] = !empty($data['customer_successful_search']->response) ? json_decode($data['customer_successful_search']->response)->data : null;
		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/loan_offer_letter', $data);
		$this->load->view('admin/includes/_footer');
	}


	public function loanofferpreview($loanid = '')
	{

		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/loanofferpreview', $data);
		$this->load->view('admin/includes/_footer');
	}

	public function generate_offer_pdf($loanid = '')
	{
		// Load the dompdf_gen library
		$this->load->library('Dompdf_gen');

		// Fetch the data you want to display in the PDF
		$data['loan'] = empty(getbyid($loanid, 'loans')) ? show_404() : getbyid($loanid, 'loans');
		$data['customer_successful_search'] = empty(getby(['customerid' => $data['loan']->customerid], 'customer_successful_search')) ? false : getby(['customerid' => $data['loan']->customerid], 'customer_successful_search');
		$data['customerData'] = !empty($data['customer_successful_search']->response) ? json_decode($data['customer_successful_search']->response)->data : null;

		// Load the view and pass the data to it
		$html = $this->load->view('admin/customers/pdfoffer', $data, TRUE);

		// Load HTML into dompdf
		$this->dompdf_gen->loadHtml($html);

		// Set paper size
		$this->dompdf_gen->setPaper('A4', 'portrait');

		// Render the PDF
		$this->dompdf_gen->render();

		// Output the generated PDF (force download)
		$this->dompdf_gen->stream("loan_offer.pdf", array("Attachment" => 1));
	}

	public function ippiscustomers($phone = '')
	{
		$this->rbac->check_operation_access();
		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/ippiscustomers', ['ippiscustomers' => getGroupedBy('ippis', 'ministry')]);
		$this->load->view('admin/includes/_footer');
	}

	public function fetch_ippiscustomers()
	{
		$columns = array(
			0 => 'id',
			1 => 'ippisno',
			2 => 'name',
			3 => 'phone',
			4 => 'accountno',
			5 => 'bank',
			6 => 'ministry',
			7 => 'onremita'
		);

		$limit = $this->input->post('length');
		$start = $this->input->post('start');
		$officeFilter = $this->input->post('officeFilter');

		$order = isset($this->input->post('order')[0]['column']) ? $columns[$this->input->post('order')[0]['column']] : 'name';
		$dir = isset($this->input->post('order')[0]['dir']) ? $this->input->post('order')[0]['dir'] : 'asc';


		$totalData = $this->Customer_model->all_ippiscustomers_count();
		$recordsFiltered = $this->Customer_model->get_filtered_ippiscustomers_count($officeFilter);

		if (empty($this->input->post('search')['value'])) {
			$ippiscustomers = $this->Customer_model->all_ippiscustomers($limit, $start, $order, $dir, $officeFilter);
		} else {
			$search = $this->input->post('search')['value'];
			$ippiscustomers = $this->Customer_model->ippiscustomers_search($limit, $start, $search, $order, $dir);
			$recordsFiltered = $this->Customer_model->ippiscustomers_search_count($search);
		}
		$data = array();
		if (!empty($ippiscustomers)) {
			foreach ($ippiscustomers as $ippiscustomer) {
				if (!empty($ippiscustomer->phone)) {
					$phone = !empty($ippiscustomer->phone) ? add_leading_zero(replace_leading_234($ippiscustomer->phone)) : '';

					$accountExists = (
						$remitaCustomerId = $this->getCustomerIdByAccountNumber($ippiscustomer->accountno)) ? '<a href="' . base_url('admin/customers/salary_loan_details/?cid=' . $remitaCustomerId) . '" class="badge badge-success">On remita</a>' : '';
					$nestedData['ippisno'] = $ippiscustomer->ippisno;
					$nestedData['name'] = $ippiscustomer->name;
					$nestedData['phone'] = $phone;
					$nestedData['accountno'] = $ippiscustomer->accountno;
					$nestedData['bank'] = $ippiscustomer->bank;
					$nestedData['ministry'] = $ippiscustomer->ministry;
					$nestedData['currentSalary'] = formatMoney($ippiscustomer->netpay);//!empty($sal) ? '<span class="text-success" title="most recent net pay">'.formatMoney($sal[0]->amount).'</span>' : formatMoney($ippiscustomer->netpay);
					$nestedData['id'] = $ippiscustomer->id;
					$nestedData['onremita'] = $accountExists;

					$data[] = $nestedData;
				}

			}
		}

		$json_data = array(
			"draw" => intval($this->input->post('draw')),
			"recordsTotal" => intval($totalData),
			"recordsFiltered" => intval($recordsFiltered),
			"data" => $data
		);

		echo json_encode($json_data);
	}


	public function getCustomerIdByAccountNumber($accountNumber)
	{

		// Adjust this query to match your database table and columns
		$query = $this->db->select("customerid")
			->from('customer_successful_search')
			->where("account_number =", $accountNumber)
			->limit(1)
			->get();

		// Return customer ID if found
		if ($query->num_rows() > 0) {
			return $query->row()->customerid;
		}

		return null; // Return null if not found
	}
	public function checkRemitaSelected()
	{
		$selectedIds = $this->input->post('ids');
		$results = [
			'found' => 0,
			'checked' => 0,
			'failed' => 0,
			'errors' => []
		];

		try {
			// Initial validation
			if (empty($selectedIds)) {
				throw new Exception('No records selected.');
			}

			$ids = array_filter($selectedIds, function ($value) {
				return !empty($value) && $value !== 'on';
			});

			if (empty($ids)) {
				throw new Exception('No valid IDs found after filtering.');
			}

			// Check wallet balance
			$totalSearchCost = count($ids) * 100;
			if ($totalSearchCost > wallet_balance($this->session->userdata('id'))) {
				throw new Exception('Insufficient wallet balance. Required: ' . formatMoney($totalSearchCost));
			}

			// Process in smaller batches
			$batchSize = 2; // Reduced batch size
			$chunks = array_chunk($ids, $batchSize);

			foreach ($chunks as $batch) {
				// Add delay between batches to prevent overload
				if ($results['checked'] > 0) {
					sleep(1); // 1 second delay between batches
				}

				// Fetch IPPIS customers for this batch
				$ippisCustomers = $this->db->select('i.*, c.remitacustomerid')
					->from('ippis i')
					->where_in('i.id', $batch)
					->join('customers c', 'c.phone = i.phone', 'left')
					->get()
					->result();

				foreach ($ippisCustomers as $customer) {
					$results['checked']++;

					try {
						// Skip if already on Remita
						if (!empty($customer->remitacustomerid)) {
							$results['found']++;
							continue;
						}

						// Set shorter timeout for API calls
						$requestId = time() . rand(1000, 9999);
						$apiHash = hash("sha512", API_KEY . $requestId . API_TOKEN);
						$headers = [
							"Content-Type" => 'application/json',
							"API_KEY" => API_KEY,
							"MERCHANT_ID" => MERCHANT_ID,
							"REQUEST_ID" => $requestId,
							"AUTHORIZATION" => "remitaConsumerKey=" . API_KEY . ", remitaConsumerToken=" . $apiHash,
							"Connection" => "close" // Close connection after request
						];

						// Try NUBAN search with timeout
						$ctx = stream_context_create([
							'http' => [
								'timeout' => 10 // 10 seconds timeout
							]
						]);

						$response = $this->performRemitaSearch(
							$customer,
							$headers,
							GET_SALARY_HISTORY_URL_NUBAN,
							'nuban'
						);

						if (empty($response) || $response->responseCode !== "00") {
							// Try phone search with timeout
							$response = $this->performRemitaSearch(
								$customer,
								$headers,
								GET_SALARY_HISTORY_URL,
								'phone'
							);
						}

						if (!empty($response) && $response->responseCode === "00") {
							$this->processSuccessfulSearch($customer, $response, $headers);
							$results['found']++;
						} else {
							$this->processFailedSearch($customer, $response);
							$results['failed']++;
							$results['errors'][] = "Failed for {$customer->phone}: " .
								($response->responseMsg ?? 'Unknown error');
						}

					} catch (Exception $e) {
						$results['failed']++;
						$results['errors'][] = "Error processing {$customer->phone}: {$e->getMessage()}";
						log_message('error', "Remita search error: " . $e->getMessage());
					}
				}
			}

			// Format response message
			$message = sprintf(
				"Search Results:\n\n✓ Found: %d\n\n🔍 Checked: %d\n\n❌ Failed: %d",
				$results['found'],
				$results['checked'],
				$results['failed']
			);

			if (!empty($results['errors'])) {
				$message .= "\n\nError Details:\n\n";
				foreach ($results['errors'] as $error) {
					$message .= "• " . $error . "\n\n";
				}
			}

			echo json_encode([
				'status' => $results['found'] > 0 ? 'success' : 'error',
				'message' => $message,
				'title' => 'Search Complete'
			]);

		} catch (Exception $e) {
			echo json_encode([
				'status' => 'error',
				'message' => $e->getMessage()
			]);
		}
	}

	private function performRemitaSearch($customer, $headers, $url, $type = 'nuban')
	{
		$body = $type === 'nuban' ?
			[
				"authorisationCode" => mt_rand(0, ************),
				"firstName" => "",
				"lastName" => "",
				"middleName" => "",
				"accountNumber" => $customer->accountno,
				"bankCode" => str_pad($customer->bankcode, 3, '0', STR_PAD_LEFT),
				"bvn" => "",
				"authorisationChannel" => "USSD"
			] :
			[
				"authorisationCode" => mt_rand(0, ************),
				"phoneNumber" => $customer->phone,
				"authorisationChannel" => "USSD"
			];

		try {
			return json_decode($this->functions->sendPostRequest($url, $headers, $body));
		} catch (Exception $e) {
			log_message('error', "Remita API error ($type search): " . $e->getMessage());
			throw new Exception("API call failed: " . $e->getMessage());
		}
	}

	// ... existing code ...

	private function processSuccessfulSearch($customer, $response, $headers)
	{
		try {
			$phone = $customer->phone;
			$customerData = [
				"remitacustomerid" => $response->data->customerId,
				"fullname" => $response->data->customerName,
				"organization" => $response->data->companyName,
				"phone" => $phone,
				"accountno" => $response->data->accountNumber,
				"bankcode" => $response->data->bankCode,
				"bvn" => $response->data->bvn,
				"office" => $customer->ministry,
				"firstpaymentdate" => $response->data->firstPaymentDate,
				"originalcustomerid" => $response->data->originalCustomerId,
				"onremita" => 1,
				"lastchecked" => date('Y-m-d H:i:s'),
				"checkedby" => $this->session->userdata('id'),
				'datecreated' => date('Y-m-d H:i:s'),
				'createdby' => $this->session->userdata('id')
			];

			// Handle salary and loan data if available
			if (!empty($response->data->salaryPaymentDetails) || !empty($response->data->loanHistoryDetails)) {
				$salaryLoanData = [
					"phone" => $phone,
					"customerid" => $response->data->customerId,
					"responseid" => $response->responseId,
					"salary" => !empty($response->data->salaryPaymentDetails) ? json_encode($response->data->salaryPaymentDetails) : null,
					"loan" => !empty($response->data->loanHistoryDetails) ? json_encode($response->data->loanHistoryDetails) : null,
				];

				// Update or insert salary loan data
				$existingSalaryLoan = getby(['phone' => $phone], 'customer_salary_loan');
				if ($existingSalaryLoan) {
					$this->db->update('customer_salary_loan', $salaryLoanData, ['phone' => $phone]);
				} else {
					$this->db->insert('customer_salary_loan', $salaryLoanData);
				}
			}

			// Log API call
			$apiCallLogData = [
				"customerid" => $response->data->customerId,
				"phone" => $phone,
				"responseid" => $response->responseId,
				"requestheader" => json_encode($headers),
				"requestbody" => json_encode($response->requestBody ?? []),
				"response" => json_encode($response),
				"created_at" => date('Y-m-d H:i:s'),
			];
			$this->db->insert('customer_successful_search', $apiCallLogData);

			// Deduct wallet balance
			$wallet_data = [
				'reference' => generate_unique_reference(15),
				'creditordebit' => 'dr',
				'amount' => 100,
				'channel' => 'remita-search',
				'balanceafter' => balance_after($this->session->userdata('id'), 'dr', 100),
				'narration' => 'Remita search charge for ' . $phone,
				'datecreated' => date('Y-m-d H:i:s'),
				'createdby' => $this->session->userdata('id'),
				'userid' => $this->session->userdata('id')
			];
			$this->db->insert('wallet_transactions', $wallet_data);

			// Update or insert customer data
			$existingCustomer = getby(['remitacustomerid' => $customerData['remitacustomerid']], 'customers');
			if ($existingCustomer) {
				$this->Common_model->saveWhere($customerData, ['remitacustomerid' => $existingCustomer->remitacustomerid], 'customers');
			} else {
				$this->Common_model->save($customerData, null, 'customers');
			}

		} catch (Exception $e) {
			log_message('error', 'Process successful search error: ' . $e->getMessage());
			throw new Exception('Failed to process successful search: ' . $e->getMessage());
		}
	}

	// ... existing code ...

	private function processFailedSearch($customer, $response)
	{
		try {
			$onremita = 0;
			if (!empty($response) && $response->responseCode === "7808") {
				$onremita = 2; // Suspended
			} elseif (!empty($response) && $response->responseCode === "7801") {
				$onremita = 0; // Not on Remita
			} else {
				$onremita = 3; // Unknown status
			}

			// Update customer status in database
			$customerData = [
				'phone' => $customer->phone,
				'onremita' => $onremita,
				'lastchecked' => date('Y-m-d H:i:s'),
				'checkedby' => $this->session->userdata('id')
			];

			// Log the failed attempt
			$failedSearchLog = [
				'phone' => $customer->phone,
				'response_code' => $response->responseCode ?? 'unknown',
				'response_message' => $response->responseMsg ?? 'No response message',
				'created_at' => date('Y-m-d H:i:s'),
				'created_by' => $this->session->userdata('id')
			];

			$this->db->trans_start();

			// Update or insert customer record
			$existingCustomer = getby(['phone' => $customer->phone], 'customers');
			if ($existingCustomer) {
				$this->Common_model->saveWhere($customerData, ['phone' => $customer->phone], 'customers');
			} else {
				$this->Common_model->save($customerData, null, 'customers');
			}

			// Log the failed search
			$this->db->insert('failed_searches', $failedSearchLog);

			$this->db->trans_complete();

			if ($this->db->trans_status() === FALSE) {
				throw new Exception('Failed to save search results');
			}

		} catch (Exception $e) {
			log_message('error', 'Process failed search error: ' . $e->getMessage());
			throw new Exception('Failed to process failed search: ' . $e->getMessage());
		}
	}


	// public function checkRemitaSingleSelection(){

	// }

	public function search($phone = '')
	{
		$this->rbac->check_operation_access();
		$data['customer_searchs'] = getalldata('customer_successful_search', null, 'id', 'DESC', 0, 4);
		$data['phone'] = $phone;
		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/search', $data);
		$this->load->view('admin/includes/_footer');
	}

	public function getIppisData()
	{
		$search_query = $this->input->post('search_query');
		$this->db->select('*');
		$this->db->from('ippis');
		$this->db->like('phone', $search_query);
		$this->db->or_like('accountno', $search_query);
		$this->db->or_like('ippisno', $search_query);

		$query = $this->db->get();
		$data = $query->result_array();
		echo json_encode($data);
	}

	public function searchippis()
	{
		$this->rbac->check_operation_access();
		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/searchippis');
		$this->load->view('admin/includes/_footer');
	}

	public function nin()
	{
		$this->rbac->check_operation_access();
		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/ninlookup');
		$this->load->view('admin/includes/_footer');
	}

	public function search_batch()
	{
		$this->rbac->check_operation_access();
		$data['customer_searchs'] = getalldata('customer_successful_search', null, 'id', 'DESC', 0, 4);
		$data['offices'] = getalldata('mdas');
		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/search_batch', $data);
		$this->load->view('admin/includes/_footer');
	}

	public function mandatepaymenthistory()
	{
		$data = null;
		if (!empty($_GET['cid'])) {
			$data['customerid'] = $_GET['cid'];
		}
		if (!empty($_GET['man'])) {
			$data['mandateref'] = $_GET['man'];
		}
		if (!empty($_GET['auth'])) {
			$data['authcode'] = $_GET['auth'];
		}
		$this->rbac->check_operation_access();
		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/mandatepaymenthistory', $data);
		$this->load->view('admin/includes/_footer');
	}

	public function search_alt()
	{
		$this->rbac->check_operation_access();
		$data['customer_searchs'] = getalldata('customer_successful_search', null, 'id', 'DESC', 0, 4);
		$data['banks'] = getalldata('banks', null, 'name', 'ASC');
		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/search_alt', $data);
		$this->load->view('admin/includes/_footer');
	}


	public function salary_loan_details($phone = NULL)
	{
		$this->rbac->check_operation_access();
		$cid = null;
		if (!empty($_GET['cid'])) {
			$cid = $_GET['cid'];
		}
		$data = null;
		if (!empty($phone)) {
			$data['customer_salary_loan'] = getby(['phone' => $phone], 'customer_salary_loan');
			$data['customer_successful_search'] = getby(['phone' => $phone], 'customer_successful_search');
		} elseif ($cid) {
			$data['customer_successful_search'] = getby(['customerid' => $cid], 'customer_successful_search');
			$data['customer_salary_loan'] = getby(['customerid' => $cid], 'customer_salary_loan');
		}



		$data['salaries'] = !empty($data['customer_salary_loan']->salary) ? json_decode($data['customer_salary_loan']->salary) : null;
		$data['loans'] = !empty($data['customer_salary_loan']->salary) ? json_decode($data['customer_salary_loan']->loan) : null;

		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/salary_loan_details', $data);
		$this->load->view('admin/includes/_footer');
	}


	public function createloan()
	{
		//$this->rbac->check_operation_access();
		if (!empty($_GET['phone'])) {
			$customer = !empty($customer = getby(['phone' => $_GET['phone']], 'customers')) ? $customer : redirect($_SERVER["HTTP_REFERER"]);
			$data['phone'] = empty($_GET['phone']) ? $_POST['phone'] : $_GET['phone'];
			$data['name'] = $customer->fullname;
			$data['bvn'] = $customer->bvn;
			$data['office'] = $customer->organization;
			$data['customer'] = $customer;
			$data['customerid'] = $customer->remitacustomerid;
			$data['lastchecked'] = $customer->lastchecked;
			$data['loans'] = getalldata('loans', ['phone' => $data['phone']]);
		} elseif (!empty($_GET['cid'])) {
			$customer = !empty($customer = getby(['remitacustomerid' => $_GET['cid']], 'customers')) ? $customer : redirect($_SERVER["HTTP_REFERER"]);
			$data['phone'] = null;
			$data['name'] = $customer->fullname;
			$data['bvn'] = $customer->bvn;
			$data['office'] = $customer->organization;
			$data['customer'] = $customer;
			$data['customerid'] = $customer->remitacustomerid;
			$data['lastchecked'] = $customer->lastchecked;
			$data['loans'] = getalldata('loans', ['customerid' => $data['customerid']], 'id', 'desc');
			$data['customer_successful_search'] = getby(['customerid' => $_GET['cid']], 'customer_successful_search');
		} else {

			$this->session->set_flashdata('error', 'Unauthorized Request');
			redirect($_SERVER["HTTP_REFERER"]);
		}

		$this->load->view('admin/includes/_header');
		$this->load->view('admin/customers/createloan', $data);
		$this->load->view('admin/includes/_footer');
	}

	public function saveloan()
	{
		//$this->rbac->check_operation_access();


		if (!empty($_POST['createloan'])) {
			$customerExists = false;
			$this->form_validation->set_rules('phone', 'Customer Phone Number', 'trim|required');
			$this->form_validation->set_rules('customername', 'Customer Name', 'trim|required');
			$this->form_validation->set_rules('principal', 'Loan Principal', 'trim|required|is_numeric');
			$this->form_validation->set_rules('tenor', 'Tenor', 'trim|required|is_numeric');
			$this->form_validation->set_rules('repayment', 'Repayment', 'trim|required|is_numeric');
			$this->form_validation->set_rules('totalrepayment', 'Total Repayment', 'trim|required|is_numeric');

			if ($this->form_validation->run() == FALSE) {
				$data = array(
					'errors' => validation_errors()
				);
				$this->session->set_flashdata('error', $data['errors']);
				redirect('admin/customers/createloan?phone=' . $_POST['phone'] . '&cid=' . $_POST['cid']);
			} else {
				$customer_has_running_loan = empty(getby(['customerid' => $_POST['cid'], 'status' => 'disbursed'], 'loans')) ? false : true;

				$customerExists = empty(getby(['remitacustomerid' => $_POST['cid']], 'customers')) ? false : getby(['remitacustomerid' => $_POST['cid']], 'customers');
				if ($customer_has_running_loan) {
					$this->session->set_flashdata('error', 'Customer already has a loan created or running');
					redirect('admin/customers/createloan?phone=' . $_POST['phone'] . '&cid=' . $_POST['cid']);
					die();
				}
				$loanData['phone'] = $_POST['phone'];
				$loanData['customerid'] = $_POST['cid'];
				$loanData['loanamount'] = $_POST['principal'];
				$loanData['rate'] = $_POST['rate'];
				$loanData['bookedwith'] = $_POST['bookedwith'];
				$loanData['numberofrepayments'] = $_POST['tenor'];
				$loanData['collectionamount'] = $_POST['repayment'];
				$loanData['totalcollectionamount'] = $_POST['totalrepayment'];
				$loanData['datecreated'] = date('Y-m-d H:i:s');
				$loanData['createdby'] = $this->session->userdata('id');
				$loanData['status'] = 'created';
				$loanData['daterequested'] = date('Y-m-d H:i:s');
				$loanData['requestedby'] = $this->session->userdata('id');

				$loanData = $this->security->xss_clean($loanData);

				$customer_successful_search = getby(['customerid' => $_POST['cid']], 'customer_successful_search');
				$data = json_decode($customer_successful_search->response, true);
				$data['data']['customerName'] = $_POST['customername'];
				$data['data']['bvn'] = $_POST['bvn'];
				$data['data']['phone'] = $_POST['phone'];
				$response = json_encode($data);
				$this->Common_model->save(['response' => $response], $customer_successful_search->id, 'customer_successful_search');


				if (empty($customerExists)) {
					$customer_successful_search = empty(getby(['customerid' => $_POST['cid']], 'customer_successful_search')) ? false : getby(['customerid' => $_POST['cid']], 'customer_successful_search');
					$customer = json_decode($customer_successful_search->response)->data;
					$responseData = json_decode($customer_successful_search->response);
					$customerData = [
						"remitacustomerid" => $customer->customerId,
						"fullname" => $_POST['customername'],
						"organization" => $customer->companyName,
						"phone" => $this->input->post('phone'),
						"accountno" => $customer->accountNumber,
						"bankcode" => $customer->bankCode,
						"bvn" => $customer->bvn,
						"firstpaymentdate" => $customer->firstPaymentDate,
						"originalcustomerid" => $customer->originalCustomerId,
						"onremita" => 1,
						"lastchecked" => $responseData->responseDate,
						"checkedby" => $this->session->userdata('id'),
						"searchedwith" => $_POST['bookedwith'],
						$this->input->post('id') ? 'datemodified' : 'datecreated' => date('Y-m-d H:i:s'),
						$this->input->post('id') ? 'modifiedby' : 'createdby' => $this->session->userdata('id')
					];

					$this->Common_model->saveWhere($customerData, ['remitacustomerid' => $_POST['cid']], 'customers');
				}
				if ($this->Common_model->save($loanData, null, 'loans')) {
					$this->session->set_flashdata('success', 'Loan Succesfully Created');
					redirect($_SERVER["HTTP_REFERER"]);
				} else {
					$this->session->set_flashdata('error', 'Error in creating loan');
					redirect($_SERVER["HTTP_REFERER"]);
				}
			}
		} else {
			$this->session->set_flashdata('error', 'Unauthorized Request');
			redirect($_SERVER["HTTP_REFERER"]);
		}
	}

	public function approveloan($id = null)
	{
		$this->rbac->check_operation_access();
		$data['loan'] = $this->Common_model->get_one($id, 'loans');
		$data['customer_successful_search'] = getby(['customerid' => $data['loan']->customerid], 'customer_successful_search');
		$this->load->view('admin/customers/approveloan', $data);
	}

	public function editcustomer($id = null)
	{
		$this->rbac->check_operation_access();

		if (isset($_POST['submit'])) {
			$this->form_validation->set_rules('customername', 'Customer Name', 'trim|required');
			$this->form_validation->set_rules('bvn', 'BVN', 'trim|required|is_numeric');

			if ($this->form_validation->run() == FALSE) {
				$data = array(
					'errors' => validation_errors()
				);
				$this->session->set_flashdata('error', $data['errors']);
				redirect($_SERVER["HTTP_REFERER"]);
			} else {
				$customer_successful_search = getby(['customerid' => $_POST['cid']], 'customer_successful_search');
				$data = json_decode($customer_successful_search->response, true);
				$data['data']['customerName'] = $_POST['customername'];
				$data['data']['bvn'] = $_POST['bvn'];
				$data['data']['phone'] = $_POST['phone'];
				$data['data']['companyName'] = $_POST['office'];
				$response = json_encode($data);

				//die(prettyprint($customer_successful_search->id));
				if ($this->Common_model->save(['response' => $response], $customer_successful_search->id, 'customer_successful_search')) {
					$this->Common_model->save(['phone' => $_POST['phone']], $_POST['loanid'], 'loans');
					$this->session->set_flashdata('success', 'Customer details updated!');
					redirect($_SERVER["HTTP_REFERER"]);
				} else {
					$this->session->set_flashdata('error', 'Error updating details');
					redirect($_SERVER["HTTP_REFERER"]);
				}
			}
		} else {
			$data['loan'] = $this->Common_model->get_one($id, 'loans');
			$data['customer_successful_search'] = getby(['customerid' => $data['loan']->customerid], 'customer_successful_search');
			//var_dump($data['loan']->customerid);die();
			$this->load->view('admin/customers/editcustomer', $data);
		}

	}



	public function saveapproval()
	{
		$this->rbac->check_operation_access();
		$loan = $this->Common_model->get_one($this->input->post('loanid'), 'loans');

		if (!empty($_POST['decline'])) {
			$loanData = [
				"status" => 'declined',
				"approvedby" => $this->session->userdata('id'),
				"dateapproved" => date('Y-m-d H:i:s'),
				"approvalcomment" => $this->input->post('comment'),
			];

			$this->Common_model->save($loanData, $loan->id, 'loans');
			echo json_encode(['success' => true, 'message' => 'Loan declined succesfully']);
		} elseif (!empty($_POST['approve'])) {

			if (!empty($loan)) {
				$phone = convertPhoneNumber($this->input->post('phone'));

				$requestId = $this->input->post('rid');
				$apiHash = hash("sha512", API_KEY . $requestId . API_TOKEN);
				$authorization = "remitaConsumerKey=" . API_KEY . ", remitaConsumerToken=" . $apiHash;


				$headers = [
					'Content-Type' => 'application/json',
					'API_KEY' => API_KEY,
					'MERCHANT_ID' => MERCHANT_ID,
					'REQUEST_ID' => $requestId,
					'AUTHORIZATION' => $authorization
				];


				if ($loan->bookedwith == 'nuban') {
					$body = [
						"authorisationCode" => $this->input->post('acode'),
						"firstName" => "",
						"lastName" => "",
						"middleName" => "",
						"accountNumber" => $this->input->post('accountno'),
						"bankCode" => $this->input->post('bank'),
						"bvn" => $this->input->post('bvn'),
						"authorisationChannel" => "USSD"
					];
				} else {
					$body = [
						"authorisationCode" => $this->input->post('acode'),
						"phoneNumber" => $this->input->post('phone'),
						"authorisationChannel" => "USSD"
					];
				}

				if (100 > wallet_balance($this->session->userdata('id'))) {
					echo json_encode(['success' => false, 'message' => 'Insufficient wallet balance']);
				}
				$url = $loan->bookedwith == 'nuban' ? URI . '/remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/salary/history/provideCustomerDetails' : GET_SALARY_HISTORY_URL;
				try {
					$response = $this->functions->sendPostRequest($url, $headers, $body);
					$salary_data_response_obj = json_decode($response);

					$customer_exists_by_phone = !empty($customer_by_phone = getby(['phone' => $this->input->post('phone')], 'customers')) ? $customer_by_phone : null;
					$customerWaitingData = [
						'phone' => $this->input->post('phone'),
						'createdby' => $this->session->userdata('id'),
						'lastchecked' => date('Y-m-d H:i:s'),
						'datecreated' => date('Y-m-d H:i:s'),
						'onremita' => 2
					];

					if (!empty($salary_data_response_obj)) {
						if ($salary_data_response_obj->responseCode == "00") {

							//disburemnt call
							$requestId = $this->input->post('rid') + 20;
							$apiHash = hash("sha512", API_KEY . $requestId . API_TOKEN);
							$authorization = "remitaConsumerKey=" . API_KEY . ", remitaConsumerToken=" . $apiHash;


							$headers = [
								'Content-Type' => 'application/json',
								'API_KEY' => API_KEY,
								'MERCHANT_ID' => MERCHANT_ID,
								'REQUEST_ID' => $requestId,
								'AUTHORIZATION' => $authorization
							];

							$disbursementRequestBody = [
								"customerId" => $salary_data_response_obj->data->customerId,
								"authorisationCode" => $this->input->post('acode'),
								"authorisationChannel" => "USSD",
								"phoneNumber" => $this->input->post('phone'),
								"accountNumber" => "**********",
								"currency" => "NGN",
								"loanAmount" => $loan->loanamount,
								"collectionAmount" => $loan->collectionamount,
								"dateOfDisbursement" => date("d-m-Y H:i:s"),
								"dateOfCollection" => date("d-m-Y H:i:s"),
								"totalCollectionAmount" => $loan->totalcollectionamount,
								"numberOfRepayments" => $loan->numberofrepayments,
								"bankCode" => "011",

							];

							try {
								$response = $this->functions->sendPostRequest(DISBURSEMENT_NOTIFICATION_URL, $headers, $disbursementRequestBody);
								$disbrusement_response_obj = json_decode($response);

								if ($disbrusement_response_obj->responseCode == 400) {
									// code...
									echo json_encode(['success' => false, 'message' => $disbrusement_response_obj->responseMsg]);
								}
							} catch (Exception $e) {
								log_message('error', $e->getMessage());
								echo json_encode(['success' => false, 'message' => 'Network error or service downtime disbursing']);
							}



							$customerData = [
								"remitacustomerid" => $salary_data_response_obj->data->customerId,
								"fullname" => $salary_data_response_obj->data->customerName,
								"organization" => $salary_data_response_obj->data->companyName,
								"phone" => $this->input->post('phone'),
								"accountno" => $salary_data_response_obj->data->accountNumber,
								"bankcode" => $salary_data_response_obj->data->bankCode,
								"bvn" => $salary_data_response_obj->data->bvn,
								"firstpaymentdate" => $salary_data_response_obj->data->firstPaymentDate,
								"originalcustomerid" => $salary_data_response_obj->data->originalCustomerId,
								"onremita" => 1,
								"lastchecked" => date('Y-m-d H:i:s'),
								"checkedby" => $this->session->userdata('id'),
								"searchedwith" => isset($_POST['altsearch']) ? 'accountno' : 'phone',
								$this->input->post('id') ? 'datemodified' : 'datecreated' => date('Y-m-d H:i:s'),
								$this->input->post('id') ? 'modifiedby' : 'createdby' => $this->session->userdata('id')
							];
							if (!empty($customer_exists_by_phone)) {
								unset($customerData['datecreated']);
								unset($customerData['createdby']);
							}
							if (!empty($salary_data_response_obj->data->salaryPaymentDetails) || !empty($salary_data_response_obj->data->loanHistoryDetails)) {
								$salaryLoanData = [
									"phone" => $this->input->post('phone'),
									"customerid" => $customerData['remitacustomerid'],
									"responseid" => $salary_data_response_obj->responseId,
									"salary" => !empty($salary_data_response_obj->data->salaryPaymentDetails) ? json_encode($salary_data_response_obj->data->salaryPaymentDetails) : null,
									"loan" => !empty($salary_data_response_obj->data->loanHistoryDetails) ? json_encode($salary_data_response_obj->data->loanHistoryDetails) : null,
								];

								$customer_salary_loan_data = !empty($customer_salary_loan = getby(['customerid' => $salaryLoanData['customerid'], 'phone' => $salaryLoanData['phone']], 'customer_salary_loan')) ? $customer_salary_loan : null;

								if (!empty($customer_salary_loan_data)) {
									$this->Common_model->saveWhere($salaryLoanData, ['phone' => $this->input->post('phone')], 'customer_salary_loan');
								} else {
									$this->Common_model->saveWhere($salaryLoanData, null, 'customer_salary_loan');
								}
							}

							$apiCallLogData = [
								"customerid" => $salary_data_response_obj->data->customerId,
								"phone" => $customerData['phone'],
								"responseid" => $salary_data_response_obj->responseId,
								"requestheader" => json_encode($headers),
								"requestbody" => json_encode($body),
								"url" => GET_SALARY_HISTORY_URL,
								"response" => json_encode($salary_data_response_obj),
								'created_at' => date('Y-m-d H:i:s')
							];

							$salary_history_wallet_data = [
								'reference' => generate_unique_reference(10),
								'creditordebit' => 'dr',
								'amount' => 100,
								'channel' => 'remita-search',
								'balanceafter' => balance_after($this->session->userdata('id'), 'dr', 100),
								'narration' => 'Remita search charge for ' . $customerData['phone'],
								'datecreated' => date('Y-m-d H:i:s'),
								'createdby' => $this->session->userdata('id'),
								'userid' => $this->session->userdata('id')


							];


							$disbursement_wallet_data = [
								'reference' => generate_unique_reference(10),
								'creditordebit' => 'dr',
								'amount' => 0,
								'channel' => 'remita-disburse',
								'balanceafter' => balance_after($this->session->userdata('id'), 'dr', 0),
								'narration' => 'Loan disbursment for ' . $customerData['phone'] . '_' . $loan->id,
								'datecreated' => date('Y-m-d H:i:s'),
								'createdby' => $this->session->userdata('id'),
								'userid' => $this->session->userdata('id')
							];

							$customer_exists_by_remita_id = !empty($customer_by_remita_id = getby(['remitacustomerid' => $customerData['remitacustomerid']], 'customers')) ? $customer_by_remita_id : null;
							$this->Common_model->save($salary_history_wallet_data, null, 'wallet_transactions');
							$this->Common_model->save($disbursement_wallet_data, null, 'wallet_transactions');
							$this->Common_model->save($apiCallLogData, null, 'customer_successful_search');
							if (!empty($customer_exists_by_phone)) {
								$this->Common_model->saveWhere($customerData, ['phone' => $customer_exists_by_phone->phone], 'customers');
							} elseif (!empty($customer_exists_by_remita_id)) {
								$this->Common_model->saveWhere($customerData, ['remitacustomerid' => $customer_exists_by_remita_id->remitacustomerid], 'customers');
							} else {
								$this->Common_model->save($customerData, null, 'customers');
							}

							if ($disbrusement_response_obj->status == "success") {
								$loanData = [
									"customerid" => $salary_data_response_obj->data->customerId,
									"authcode" => $disbursementRequestBody['authorisationCode'],
									"mandateref" => $disbrusement_response_obj->data->mandateReference,
									"transactionref" => generate_unique_reference(10),
									"dateofcollection" => $disbursementRequestBody['dateOfCollection'],
									"dateofdisbursement" => $disbursementRequestBody['dateOfDisbursement'],
									"requestid" => $headers['REQUEST_ID'],
									"requestbody" => json_encode($disbursementRequestBody),
									"response" => json_encode($disbrusement_response_obj),
									"customeraccountno" => $salary_data_response_obj->data->accountNumber,
									"customerbankcode" => $salary_data_response_obj->data->bankCode,
									"status" => 'disbursed',
									"approvedby" => $this->session->userdata('id'),
									"dateapproved" => date('Y-m-d H:i:s'),
									"approvalcomment" => $this->input->post('comment'),
									"disbursedby" => $this->session->userdata('id'),
									"datedisbursed" => date('Y-m-d H:i:s'),
								];

								$offerUrl = base_url() . 'loanoffer/' . custom_encode_id($loan->id);
								$shortend_url = get_tinyurl($offerUrl);
								$otp = generate_otp();
								$message = 'Dear Customer, Your CONSUMERMFB Credit of ' . formatMoney($loan->loanamount) . ' is Approved. Click ' . $shortend_url . ' and Enter OTP ' . $otp . ' to accept the offer. OTP expires in 24HRS';
								//$smsResponse = sendsms($message, $phone, $channel = "generic", $smstype = "transactional");

								$smsResponse = sendsms($message, $phone, "generic", "transactional", "hollatags");

								$encrypted_otp = custom_encode_id($otp);
								$loanData['token'] = $encrypted_otp;
								$loanData['otp_expires_at'] = date('Y-m-d H:i:s', strtotime('+24 hours'));
								$loanData['generatedby '] = $this->session->userdata('id');
								$loanData['tokengenerationdate'] = date('Y-m-d H:i:s');


								if ($this->Common_model->save($loanData, $loan->id, 'loans')) {
									echo json_encode(['success' => true, 'message' => 'Loan approval was successful']);
								} else {
									echo json_encode(['success' => false, 'message' => 'Loan disbursment was successful but failed to save']);
								}
							} else {
								echo json_encode(['success' => false, 'message' => $disbrusement_response_obj->responseMsg . ' on remita']);
							}
						} elseif ($salary_data_response_obj->responseCode == "7801" || $salary_data_response_obj->responseCode == "7808") {

							$onremita = $salary_data_response_obj->responseCode == "7808" ? 2 : 0;
							if (empty($customer_exists_by_phone)) {
								$this->Common_model->save($customerWaitingData, null, 'customers');
							}
							echo json_encode(['success' => false, 'message' => $salary_data_response_obj->responseMsg . ' on remita']);
						}
					} else {
						$this->Common_model->save($customerWaitingData, null, 'customers');
					}
				} catch (Exception $e) {
					log_message('error', $e->getMessage());
					echo json_encode(['success' => false, 'message' => 'Network error or service downtime searching salary history']);

				}
			} else {
				echo json_encode(['success' => false, 'message' => 'Wrong request']);

			}
		}
	}


	public function stoploan($id)
	{
		$this->rbac->check_operation_access();

		$data['loan'] = $this->Common_model->get_one($id, 'loans');
		$data['customer_successful_search'] = getby(['customerid' => $data['loan']->customerid], 'customer_successful_search');
		$this->load->view('admin/customers/stoploan', $data);
	}

	public function savestoploan()
	{
		$this->rbac->check_operation_access();
		$loan = $this->Common_model->get_one($this->input->post('loanid'), 'loans');

		if (!empty($_POST['approve']) && !empty($loan->authcode) && !empty($loan->authcode)) {
			$customer = $this->input->post('customerid');
			$loanData = [
				"status" => 'closed',
				"terminator" => $this->session->userdata('id'),
				"dateterminated" => date('Y-m-d H:i:s'),
				"terminationcomment" => $this->input->post('comment'),
			];


			$requestId = $this->input->post('rid');
			$apiHash = hash("sha512", API_KEY . $requestId . API_TOKEN);
			$authorization = "remitaConsumerKey=" . API_KEY . ", remitaConsumerToken=" . $apiHash;


			$headers = [
				'Content-Type' => 'application/json',
				'API_KEY' => API_KEY,
				'MERCHANT_ID' => MERCHANT_ID,
				'REQUEST_ID' => $requestId,
				'AUTHORIZATION' => $authorization
			];


			$body = [
				"authorisationCode" => $loan->authcode,
				"customerId" => $customer,
				"mandateReference" => $loan->mandateref
			];

			$url = URI . '/remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/stop/loan';
			try {
				$response = $this->functions->sendPostRequest($url, $headers, $body);
				$stoploan_response_obj = json_decode($response);
				log_message('error', $response);
			} catch (Exception $e) {
				log_message('error', $e->getMessage());
				$this->session->set_flashdata('error', 'Network error or service downtime terminating loan');
				redirect('admin/customers/loans');
				die();
			}
			if ($stoploan_response_obj->responseCode == "00") {
				$this->Common_model->save($loanData, $loan->id, 'loans');
				$this->session->set_flashdata('success', 'Loan terminated successfuly');
				redirect('admin/customers/loans');
			} else {
				$this->session->set_flashdata('error', $stoploan_response_obj->responseMsg);
				redirect('admin/customers/loans');
			}
		} else {
			$this->Common_model->save($loanData, $loan->id, 'loans');
			$this->session->set_flashdata('success', 'Loan termination failed or loan not found');
		}
	}

	public function closeloan($loanid = 0)
	{
		$this->rbac->check_operation_access();
		$loan = $this->Common_model->get_one($loanid, 'loans');

		if ($loan->status != 'disbursed' && $loan->status == 'created') {
			$loanData = [
				"status" => 'closed',
				"modifiedby" => $this->session->userdata('id'),
				"datemodified" => date('Y-m-d H:i:s'),
			];
			if ($this->Common_model->save($loanData, $loan->id, 'loans')) {
				$this->session->set_flashdata('success', 'Loan closed successfuly');
				redirect('admin/customers/loans');
			} else {
				$this->session->set_flashdata('error', 'loan closing failed. contact administrator');
				redirect('admin/customers/loans');
			}
		} else {
			$this->session->set_flashdata('error', 'Loan closing failed or loan not found');
			redirect('admin/customers/loans');
		}
	}

	public function upload()
	{
		$this->rbac->check_operation_access();
		$config['upload_path'] = './uploads/';
		$config['allowed_types'] = 'csv';
		$config['max_size'] = 5120; // 5MB

		$customerData = [];
		$foundData = [];
		$totalCustomersFound = 0;
		$totalCustomersChecked = 0;
		$totalCustomersCheckFailed = 0;
		$error = "";
		$phone_arr = NULL;
		$nubanSearchArray = [];
		$nubanEndPoint = URI . '/remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/salary/history/provideCustomerDetails';

		if (empty($this->input->post('mode'))) {
			echo json_encode(['success' => false, 'message' => 'Unknow Request']);
		}

		$this->load->library('upload', $config);

		if (!$this->upload->do_upload('csv_file')) {
			$error = $this->upload->display_errors();
			echo json_encode(['success' => false, 'message' => $error]);
		} else {
			$criteria = $this->input->post('criteria');

			$csvData = file_get_contents($_FILES['csv_file']['tmp_name']);
			$rows = explode("\n", $csvData);
			$totalRecords = 0;
			foreach ($rows as $row) {
				$values = str_getcsv($row);

				if (!empty($values)) {
					$totalRecords++;
					if ($criteria == "phone") {
						if (strtolower($values[0]) != "phone") {
							$phone_arr[] = add_leading_zero(replace_leading_234($values[0]));
						}
					}
					if ($criteria == "nuban") {
						$nubanSearchArray[] = [
							"accountNumber" => $values[0],
							"bankCode" => $values[1]
						];
					}

				}
			}

			$phoneNos = array_filter($phone_arr, function ($value) {
				return ($value !== '' && $value !== null && $value !== false && $value != '0');
			});


			$totalCriteriaRows = ($criteria == 'phone') ? sizeof($phoneNos) : sizeof($nubanSearchArray);
			$totalCriteriaSearchCost = $totalCriteriaRows * 100;
			$office = null;

			if ($this->input->post('office') == "others") {
				$office = $this->input->post('other_office');
			} elseif ($this->input->post('office') == "no") {
				$office = $this->input->post('office');
			} else {
				$office = $this->input->post('office');
			}

			if ($totalCriteriaSearchCost > wallet_balance($this->session->userdata('id')) && $this->input->post('mode') == 2) {
				echo json_encode(['success' => false, 'message' => 'Wallet has insufficient balance to perform this search']);
			}
			if ($totalCriteriaRows > 500) {
				echo json_encode(['success' => false, 'message' => 'Total records must not exceed 500']);
			} else {
				if ($criteria == 'phone') {
					foreach ($phoneNos as $phone) {

						$customer = !empty($customer = getalldata('customers', ['phone' => $phone])) ? $customer : null;
						if ($this->input->post('mode') == 1) {

							if ($phone != $customer[0]->phone) {
								$customerWaitingData = [
									'phone' => $phone,
									'createdby' => $this->session->userdata('id'),
									'lastchecked' => date('Y-m-d H:i:s'),
									'datecreated' => date('Y-m-d H:i:s'),
									'onremita' => 0
								];
								$this->Common_model->save($customerWaitingData, null, 'customers');
							}
						} elseif ($this->input->post('mode') == 2) {
							$requestId = time();
							$response = null;
							$apiHash = hash("sha512", API_KEY . $requestId . API_TOKEN);
							$authorization = "remitaConsumerKey=" . API_KEY . ", remitaConsumerToken=" . $apiHash;
							$headers = [
								"Content-Type" => 'application/json',
								"API_KEY" => API_KEY,
								"MERCHANT_ID" => MERCHANT_ID,
								"REQUEST_ID" => $requestId,
								"AUTHORIZATION" => $authorization,
							];

							$body = [
								"authorisationCode" => mt_rand(0, ************),
								"phoneNumber" => $phone,
								"authorisationChannel" => "USSD"
							];


							try {
								$response = $this->functions->sendPostRequest(GET_SALARY_HISTORY_URL, $headers, $body);
								$totalCustomersChecked++;
								$response_obj = json_decode($response);
								if ($response_obj->status == 91) {
									log_message('error', $response);
								} elseif ($response_obj->status == "success") {
									$totalCustomersFound++;
									$customerData = [
										"remitacustomerid" => $response_obj->data->customerId,
										"fullname" => $response_obj->data->customerName,
										"organization" => $response_obj->data->companyName,
										"phone" => $phone,
										"accountno" => $response_obj->data->accountNumber,
										"bankcode" => $response_obj->data->bankCode,
										"bvn" => $response_obj->data->bvn,
										"office" => $office,
										"firstpaymentdate" => $response_obj->data->firstPaymentDate,
										"originalcustomerid" => $response_obj->data->originalCustomerId,
										"searchedwith" => isset($_POST['altsearch']) ? 'accountno' : 'phone',
										"onremita" => 1,
										"lastchecked" => date('Y-m-d H:i:s'),
										"checkedby" => $this->session->userdata('id'),
										$this->input->post('id') ? 'datemodified' : 'datecreated' => date('Y-m-d H:i:s'),
										$this->input->post('id') ? 'modifiedby' : 'createdby' => $this->session->userdata('id')
									];

									if (!empty($response_obj->data->salaryPaymentDetails) || !empty($response_obj->data->loanHistoryDetails)) {
										//collect salary and loan data
										$salaryLoanData = [
											"phone" => $phone,
											"customerid" => $response_obj->data->customerId,
											"responseid" => $response_obj->responseId,
											"salary" => !empty($response_obj->data->salaryPaymentDetails) ? json_encode($response_obj->data->salaryPaymentDetails) : null,
											"loan" => !empty($response_obj->data->loanHistoryDetails) ? json_encode($response_obj->data->loanHistoryDetails) : null,
										];

										$customer_salary_loan_data = !empty($customer_salary_loan = getalldata('customer_salary_loan', ['phone' => $phone])) ? $customer_salary_loan : null;

										if (!empty($customer_salary_loan_data)) {
											$this->db->update('customer_salary_loan', $salaryLoanData, ['phone' => $phone]);
										} else {
											$this->db->insert('customer_salary_loan', $salaryLoanData);
										}
									}

									$apiCallLogData = [
										"customerid" => $response_obj->data->customerId,
										"phone" => $phone,
										"responseid" => $response_obj->responseId,
										"requestheader" => json_encode($headers),
										"requestbody" => json_encode($body),
										"url" => GET_SALARY_HISTORY_URL,
										"response" => json_encode($response_obj),
										'created_at' => date('Y-m-d H:i:s')

									];

									$this->db->insert('customer_successful_search', $apiCallLogData);

									$wallet_data = [
										'reference' => generate_unique_reference(15),
										'creditordebit' => 'dr',
										'amount' => 100,
										'channel' => 'remita-search',
										'balanceafter' => balance_after($this->session->userdata('id'), 'dr', 100),
										'narration' => 'Remita search charge for ' . $phone,
										'datecreated' => date('Y-m-d H:i:s'),
										'createdby' => $this->session->userdata('id'),
										'userid' => $this->session->userdata('id')

									];

									$this->db->insert('wallet_transactions', $wallet_data);

									if (empty($customer)) {
										$this->db->insert('customers', $customerData);
									} else {

										$this->db->update('customers', $customerData, ['phone' => $phone]);
									}
								} else {

									$onremita = $response_obj->responseCode == "7808" ? 2 : 0;
									$customerWaitingData = [
										'phone' => $phone,
										'createdby' => $this->session->userdata('id'),
										'lastchecked' => date('Y-m-d H:i:s'),
										'onremita' => $onremita
									];
									$this->db->update('customers', $customerWaitingData, ['phone' => $phone]);
								}
							} catch (Exception $e) {
								$error = "Some errors were encountered.";
								log_message('error', $e->getMessage());

							}
						}
					}

					if ($criteria == 'nuban') {
						foreach ($nubanSearchArray as $nubanData) {
							$customer = !empty($customer = getalldata('customers', ['phone' => $nubanData[0]])) ? $customer : null;

							$requestId = time();
							$response = null;
							$apiHash = hash("sha512", API_KEY . $requestId . API_TOKEN);
							$authorization = "remitaConsumerKey=" . API_KEY . ", remitaConsumerToken=" . $apiHash;
							$headers = [
								"Content-Type" => 'application/json',
								"API_KEY" => API_KEY,
								"MERCHANT_ID" => MERCHANT_ID,
								"REQUEST_ID" => $requestId,
								"AUTHORIZATION" => $authorization,
							];



							$body = [
								"authorisationCode" => mt_rand(0, ************),
								"firstName" => "",
								"lastName" => "",
								"middleName" => "",
								"accountNumber" => $nubanData[0],
								"bankCode" => str_pad($nubanData[1], 3, '0', STR_PAD_LEFT),
								"bvn" => "",
								"authorisationChannel" => "USSD"
							];


							try {
								$response = $this->functions->sendPostRequest($nubanEndPoint, $headers, $body);
								$totalCustomersChecked++;
								$response_obj = json_decode($response);
								if ($response_obj->status == 91) {
									log_message('error', $response);
								} elseif ($response_obj->status == "success") {
									$totalCustomersFound++;
									$customerData = [
										"remitacustomerid" => $response_obj->data->customerId,
										"fullname" => $response_obj->data->customerName,
										"organization" => $response_obj->data->companyName,
										"phone" => $phone,
										"accountno" => $response_obj->data->accountNumber,
										"bankcode" => $response_obj->data->bankCode,
										"bvn" => $response_obj->data->bvn,
										"office" => $office,
										"firstpaymentdate" => $response_obj->data->firstPaymentDate,
										"originalcustomerid" => $response_obj->data->originalCustomerId,
										"searchedwith" => 'accountno',
										"onremita" => 1,
										"lastchecked" => date('Y-m-d H:i:s'),
										"checkedby" => $this->session->userdata('id'),
										'datecreated' => date('Y-m-d H:i:s'),
										'createdby' => $this->session->userdata('id')
									];

									if (!empty($response_obj->data->salaryPaymentDetails) || !empty($response_obj->data->loanHistoryDetails)) {
										//collect salary and loan data
										$salaryLoanData = [
											"phone" => $response_obj->data->customerId,
											"customerid" => $response_obj->data->customerId,
											"responseid" => $response_obj->responseId,
											"salary" => !empty($response_obj->data->salaryPaymentDetails) ? json_encode($response_obj->data->salaryPaymentDetails) : null,
											"loan" => !empty($response_obj->data->loanHistoryDetails) ? json_encode($response_obj->data->loanHistoryDetails) : null,
										];

										$customer_salary_loan_data = !empty($customer_salary_loan = getalldata('customer_salary_loan', ['phone' => $phone])) ? $customer_salary_loan : null;

										if (!empty($customer_salary_loan_data)) {
											$this->db->update('customer_salary_loan', $salaryLoanData, ['customerid' => $response_obj->data->customerId]);
										} else {
											$this->db->insert('customer_salary_loan', $salaryLoanData);
										}
									}

									$apiCallLogData = [
										"customerid" => $response_obj->data->customerId,
										"phone" => $response_obj->data->customerId,
										"responseid" => $response_obj->responseId,
										"requestheader" => json_encode($headers),
										"requestbody" => json_encode($body),
										"url" => GET_SALARY_HISTORY_URL,
										"response" => json_encode($response_obj),
										'created_at' => date('Y-m-d H:i:s')
									];

									$this->db->insert('customer_successful_search', $apiCallLogData);

									$wallet_data = [
										'reference' => generate_unique_reference(15),
										'creditordebit' => 'dr',
										'amount' => 100,
										'channel' => 'remita-search',
										'balanceafter' => balance_after($this->session->userdata('id'), 'dr', 100),
										'narration' => 'Remita search charge for customer with id' . $response_obj->data->customerId,
										'datecreated' => date('Y-m-d H:i:s'),
										'createdby' => $this->session->userdata('id'),
										'userid' => $this->session->userdata('id')

									];

									$this->db->insert('wallet_transactions', $wallet_data);

									if (empty($customer)) {
										$this->db->insert('customers', $customerData);
									} else {

										$this->db->update('customers', $customerData, ['remitacustomerid' => $response_obj->data->customerId]);
									}
								}
							} catch (Exception $e) {
								$error = "Some errors were encountered.";
								log_message('error', $e->getMessage());

							}
						}
					}
				}
			}
			$successMessage = 'Upload Complete. <b>' . $totalCustomersChecked . '</b> Checked...<b>' . $totalCustomersFound . '</b> found on remita.' . $error;
			echo json_encode(['uploadMsg' => $successMessage]);
		}
	}

	public function stslrequests()
	{
		$this->rbac->check_operation_access();
		$token = generate_fhub_token();
		$url = URI . '/loanservice/rest/api/partner/loan/applicants/list/' . $this->settings->fhub_code . '/1/5';
		$response = $this->functions->sendGetRequest($url, ['Authorization' => 'Bearer ' . $token]);
		$response = json_decode($response);
		if ($response === null && json_last_error() !== JSON_ERROR_NONE) {
			$data['loanrequests'] = null;
		} else {
			if ($response->responseCode == 200) {

				$loanData = [];

				//  "loanId" => 7,
				// "loanReference": "LOAN-20230711-PC8GRVU976OZD5I",
				// "requestedAmount": 80000,
				// "loanType": "Payday Loan",
				// "loanStatus": "Disbursed",
				// "partnerName": "LENDERCODE",
				// "partnerAvatar": "https://www.google.com",
				// "dateCreated": "2023-07-11 15:26:00",
				// "dateRequested": "2023-07-11 15:26:00",
				// "dateApproved": "2023-07-11 15:27:20",
				// "disbursementAmount": 80000,
				// "firstName": "Abdullahi",
				// "lastName": "Abdulmalik",
				// "phoneNumber": "+*************"


				$data['loanrequests'] = $response->data;
			} else {
				$data['loanrequests'] = null;
			}
		}
		$this->load->view('admin/includes/_header');
		$this->load->view('admin/general_settings/fhubtenures', $data);
		$this->load->view('admin/includes/_footer');
	}



	public function get_salary_history()
	{
		//$this->rbac->check_operation_access();
		if ($this->input->post('customerphone') || $this->input->post('accountno')) {

			//remita hash details
			$requestId = $this->input->post('rid');
			$apiHash = hash("sha512", API_KEY . $requestId . API_TOKEN);
			$authorization = "remitaConsumerKey=" . API_KEY . ", remitaConsumerToken=" . $apiHash;

			//remita request headers
			$headers = [
				'Content-Type' => 'application/json',
				'API_KEY' => API_KEY,
				'MERCHANT_ID' => MERCHANT_ID,
				'REQUEST_ID' => $requestId,
				'AUTHORIZATION' => $authorization
			];

			//set remita request body 
			if (!empty($_POST['altsearch'])) {
				$body = [
					"authorisationCode" => $this->input->post('acode'),
					"firstName" => "",
					"lastName" => "",
					"middleName" => "",
					"accountNumber" => $this->input->post('accountno'),
					"bankCode" => $this->input->post('bank'),
					"bvn" => "",
					"authorisationChannel" => "USSD"
				];
			} else {
				$body = [
					"authorisationCode" => $this->input->post('acode'),
					"phoneNumber" => trim($this->input->post('customerphone')),
					"authorisationChannel" => "USSD"
				];
			}
			//debit wallet for transaction
			if (100 > wallet_balance($this->session->userdata('id'))) {
				echo json_encode(['status' => 91, "msg" => "Insufficient wallet balance"]);
				die();
			}

			//set remita endpoint
			$url = isset($_POST['altsearch']) ? URI . '/remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/salary/history/provideCustomerDetails' : GET_SALARY_HISTORY_URL;
			try {
				$response = $this->functions->sendPostRequest($url, $headers, $body);
				$response_obj = json_decode($response);

				if (json_last_error() !== JSON_ERROR_NONE) {
					throw new Exception("Invalid response format received from server");
				}
				$customer_exists_by_phone = !empty($customer_by_phone = getby(['phone' => $this->input->post('customerphone')], 'customers')) ? $customer_by_phone : null;
				$customerWaitingData = [
					'phone' => $this->input->post('customerphone'),
					'createdby' => $this->session->userdata('id'),
					'lastchecked' => date('Y-m-d H:i:s'),
					'datecreated' => date('Y-m-d H:i:s'),
					'onremita' => 2
				];


				if (!empty($response_obj)) {
					if ($response_obj->responseCode == "00") {

						$customerData = [
							"remitacustomerid" => $response_obj->data->customerId,
							"fullname" => $response_obj->data->customerName,
							"organization" => $response_obj->data->companyName,
							"phone" => $this->input->post('customerphone'),
							"accountno" => $response_obj->data->accountNumber,
							"bankcode" => $response_obj->data->bankCode,
							"bvn" => $response_obj->data->bvn,
							"firstpaymentdate" => $response_obj->data->firstPaymentDate,
							"originalcustomerid" => $response_obj->data->originalCustomerId,
							"searchedwith" => isset($_POST['altsearch']) ? 'accountno' : 'phone',
							"onremita" => 1,
							"lastchecked" => date('Y-m-d H:i:s'),
							"checkedby" => $this->session->userdata('id'),
							$this->input->post('id') ? 'datemodified' : 'datecreated' => date('Y-m-d H:i:s'),
							$this->input->post('id') ? 'modifiedby' : 'createdby' => $this->session->userdata('id')
						];
						if (!empty($customer_exists_by_phone)) {
							unset($customerData['datecreated']);
							unset($customerData['createdby']);
						}
						if (!empty($response_obj->data->salaryPaymentDetails) || !empty($response_obj->data->loanHistoryDetails)) {
							$salaryLoanData = [
								"phone" => $this->input->post('customerphone'),
								"customerid" => $customerData['remitacustomerid'],
								"responseid" => $response_obj->responseId,
								"salary" => !empty($response_obj->data->salaryPaymentDetails) ? json_encode($response_obj->data->salaryPaymentDetails) : null,
								"loan" => !empty($response_obj->data->loanHistoryDetails) ? json_encode($response_obj->data->loanHistoryDetails) : null,
							];

							$customer_salary_loan_data = !empty($customer_salary_loan = getby(['customerid' => $salaryLoanData['customerid'], 'phone' => $salaryLoanData['phone']], 'customer_salary_loan')) ? $customer_salary_loan : null;

							if (!empty($customer_salary_loan_data)) {
								$this->Common_model->saveWhere($salaryLoanData, ['customerid' => $salaryLoanData['customerid']], 'customer_salary_loan');
							} else {
								$this->Common_model->saveWhere($salaryLoanData, null, 'customer_salary_loan');
							}
						}

						$apiCallLogData = [
							"customerid" => $response_obj->data->customerId,
							"phone" => $customerData['phone'],
							"responseid" => $response_obj->responseId,
							"requestheader" => json_encode($headers),
							"requestbody" => json_encode($body),
							"url" => GET_SALARY_HISTORY_URL,
							"response" => json_encode($response_obj),
							'created_at' => date('Y-m-d H:i:s')

						];

						$wallet_data = [
							'reference' => generate_unique_reference(10),
							'creditordebit' => 'dr',
							'amount' => 100,
							'channel' => 'remita-search',
							'balanceafter' => balance_after($this->session->userdata('id'), 'dr', 100),
							'narration' => 'Remita search charge for ' . $customerData['phone'],
							'datecreated' => date('Y-m-d H:i:s'),
							'createdby' => $this->session->userdata('id'),
							'userid' => $this->session->userdata('id')


						];

						$customer_exists_by_remita_id = !empty($customer_by_remita_id = getby(['remitacustomerid' => $customerData['remitacustomerid']], 'customers')) ? $customer_by_remita_id : null;
						$this->Common_model->save($wallet_data, null, 'wallet_transactions');
						$this->Common_model->save($apiCallLogData, null, 'customer_successful_search');
						if (!empty($customer_exists_by_phone->phone)) {
							$this->Common_model->saveWhere($customerData, ['phone' => $customer_exists_by_phone->phone], 'customers');
						} elseif (!empty($customer_exists_by_remita_id->remitacustomerid)) {
							$this->Common_model->saveWhere($customerData, ['remitacustomerid' => $customer_exists_by_remita_id->remitacustomerid], 'customers');
						} else {

							$this->Common_model->save($customerData, null, 'customers');
						}
						echo $response;
					} elseif ($response_obj->responseCode == "7801" || $response_obj->responseCode == "7808") {

						$onremita = $response_obj->responseCode == "7808" ? 2 : 0;
						if (empty($customer_exists_by_phone)) {
							$this->Common_model->save($customerWaitingData, null, 'customers');
						}
						echo $response;
					} else {
						echo $response;
					}
				}

			} catch (GuzzleHttp\Exception\ConnectException $e) {
				log_message('error', 'Remita API Connection Error: ' . $e->getMessage());
				echo json_encode([
					'status' => 91,
					'responseCode' => '91',
					'responseMsg' => 'Unable to connect to Remita server. Please try again later.',
					'isNetworkError' => true
				]);
				exit();
			} catch (GuzzleHttp\Exception\RequestException $e) {
				log_message('error', 'Remita API Request Error: ' . $e->getMessage());
				echo json_encode([
					'status' => 91,
					'responseCode' => '91',
					'responseMsg' => 'Failed to process request. Please try again.',
					'isNetworkError' => true
				]);
				exit();
			} catch (Exception $e) {
				log_message('error', 'Remita API Error: ' . $e->getMessage());
				echo json_encode([
					'status' => 91,
					'responseCode' => '91',
					'responseMsg' => 'Service temporarily unavailable',
					'isNetworkError' => true
				]);
				exit();
			}
		} else {
			echo json_encode(['status' => "fail", "responseMsg" => "wrong request"]);
			exit();
		}
	}

	public function fetch_customers()
	{

		$columns = array_combine(
			range(0, 8),
			['id', 'name', 'phone', 'customerid', 'salary', 'loans', 'status', 'lastchecked', 'actions']
		);

		$limit = $this->input->post('length');
		$start = $this->input->post('start');
		$statusFilter = !empty($this->input->post('statusFilter')) ? intval($this->input->post('statusFilter')) : 1;
		$startDate = $this->input->post('start_date');
		$endDate = $this->input->post('end_date');

		// Validate and format dates for SQL query
		if (!empty($startDate)) {
			$startDate = date('Y-m-d 00:00:00', strtotime($startDate));
		}
		if (!empty($endDate)) {
			$endDate = date('Y-m-d 23:59:59', strtotime($endDate));
		}

		$order = isset($this->input->post('order')[0]['column']) ? $columns[$this->input->post('order')[0]['column']] : 'id';
		$dir = isset($this->input->post('order')[0]['dir']) ? $this->input->post('order')[0]['dir'] : 'desc';

		// Build date filter conditions
		$dateFilter = [];
		if (!empty($startDate)) {
			$dateFilter['lastchecked >='] = $startDate;
		}
		if (!empty($endDate)) {
			$dateFilter['lastchecked <='] = $endDate;
		}

		$totalData = datatableCountAll('customers');

		// Add date conditions to status filter
		$filterConditions = ['onremita' => $statusFilter];
		if (!empty($dateFilter)) {
			$filterConditions = array_merge($filterConditions, $dateFilter);
		}
		$recordsFiltered = datatableCountFilter('customers', 'onremita', $statusFilter, $dateFilter);

		if (empty($this->input->post('search')['value'])) {
			$customers = allDatatableData(
				'customers',
				$limit,
				$start,
				$order,
				$dir,
				'onremita',
				$statusFilter,
				'lastchecked',
				$startDate,
				$endDate
			);
		} else {
			$search = $this->input->post('search')['value'];
			$customers = datatableSearch('customers', $limit, $start, $search, $order, $dir);
			$recordsFiltered = datatableSearchCount('customers', $search);
		}
		$data = array();
		if (!empty($customers)) {
			foreach ($customers as $customer) {

				$onremita = '';
				$no_of_searchs = countwhere('customer_successful_search', ['customerid' => $customer->remitacustomerid]);
				if ($customer->onremita == 1) {
					$onremita = '<span class="badge badge-success">on remita</span>';
				} elseif ($customer->onremita == 2) {
					$onremita = '<span class="badge badge-info">suspended</span>';
				} elseif ($customer->onremita == 3) {
					$onremita = '<span class="badge badge-secondary">unknown</span>';
				} else {
					$onremita = '<span class="badge badge-danger">not on remita</span>';
				}

				$onremita .= '<span class="badge badge-warning ml-2">' . $no_of_searchs . ' <i class="fa fa-search"></i></span>';
				$office = empty($customer->office) ? '' : '<small>(' . $customer->office . ')</small>';
				if (!empty(getbyid($customer->office, 'mdas'))) {
					$mda = getbyid($customer->office, 'mdas');
					$department = !empty($mda->department) ? 'Dep: ' . $mda->department : '';
					$agency = !empty($mda->agency) ? 'Agen: ' . $mda->agency : '';
					$ministry = !empty($mda->ministry) ? 'Min: ' . $mda->ministry : '';
					$office = '(' . $ministry . ' ' . $department . ' ' . $agency . ')';
				}

				$salary = !empty(getby(['phone' => $customer->phone], 'customer_salary_loan')) ? getby(['phone' => $customer->phone], 'customer_salary_loan') : null;
				$sal = !empty($salary) ? json_decode($salary->salary) : null;
				$loans = !empty($salary->loan) ? sizeof(json_decode($salary->loan)) : null;


				$nestedData['action'] = '';
				$nestedData['name'] = '<a href="' . base_url('admin/customers/salary_loan_details/?phone=' . $customer->phone . '&cid=' . $customer->remitacustomerid) . '" class="" title="View Customer Profile" >' . $customer->fullname . '</a>';

				$nestedData['phone'] = '<a href="' . base_url('admin/customers/search/' . $customer->phone) . '" title="click to recheck remita" class="text-dark">' . $customer->phone . '</a>';
				$nestedData['customerid'] = $customer->remitacustomerid;
				$nestedData['salary'] = !empty($sal) ? formatMoney($sal[0]->amount) : '<span class="badge badge-danger"> no data </span>';
				$nestedData['loans'] = !empty($loans) ? $loans : '<span class="badge bg-success">none</span>';
				$nestedData['status'] = $onremita;
				$nestedData['onremita'] = $customer->onremita;
				$nestedData['id'] = $customer->id;
				$nestedData['office'] = $customer->office;
				$nestedData['lastchecked'] = formatDate($customer->lastchecked);
				$nestedData['actions'] = '<a href="' . base_url('admin/customers/salary_loan_details/?phone=' . $customer->phone . '&cid=' . $customer->remitacustomerid) . '" title="view details" class="btn btn-default btn-sm"><i class="fa fa-eye"></i></a>';
				$data[] = $nestedData;

			}
		}

		$json_data = array(
			"draw" => intval($this->input->post('draw')),
			"recordsTotal" => intval($totalData),
			"recordsFiltered" => intval($recordsFiltered),
			"data" => $data
		);

		echo json_encode($json_data);
	}


	public function get_mandate_payment_history()
	{
		//$this->rbac->check_operation_access();
		if ($this->input->post('mandateref') && $this->input->post('customerid') && $this->input->post('authcode')) {
			$requestId = time();
			$apiHash = hash("sha512", API_KEY . $requestId . API_TOKEN);
			$authorization = "remitaConsumerKey=" . API_KEY . ", remitaConsumerToken=" . $apiHash;
			$headers = [
				'Content-Type' => 'application/json',
				'API_KEY' => API_KEY,
				'MERCHANT_ID' => MERCHANT_ID,
				'REQUEST_ID' => $requestId,
				'AUTHORIZATION' => $authorization
			];

			$body = [
				"authorisationCode" => $this->input->post('authcode'),
				"customerId" => $this->input->post('customerid'),
				"mandateRef" => $this->input->post('mandateref')
			];
			$response = $this->functions->sendPostRequest(MANDATE_PAYMENT_HISTORY, $headers, $body);
			$response_obj = json_decode($response);

			$this->session->set_tempdata('customer', $response_obj, 300);
			redirect($_SERVER['HTTP_REFERER']);
		} else {
			$this->session->set_flashdata('error', 'Invalid Request');
			redirect($_SERVER['HTTP_REFERER']);
		}
	}


	public function add_to_wait()
	{
		//$this->rbac->check_operation_access();
		if ($this->input->post('phone')) {
			$customer_waiting_data = [
				'phone' => $this->input->post('phone'),
				'createdby' => $this->session->userdata('id'),
				'lastchecked' => date('Y-m-d H:i:s')
			];

			$customer_waiting = !empty($customer = getby(['phone' => $this->input->post('phone')], 'customers_waiting')) ? $customer_waiting : null;
			if (empty($customer_waiting)) {
				$this->Common_model->save($customer_data, null, 'customers_waiting');
			}
		} else {
			redirect($_SERVER['HTTP_REFERER']);
		}
	}

	public function get_customer_suggestion()
	{

		$key = $_REQUEST["name"];
		$suggestion = array();

		$customers = $this->Customer_model->get_customers_suggestion($key);

		foreach ($customers as $customer) {
			$suggestion[] = array("id" => $customer->id, "name" => $customer->name);
		}

		echo json_encode($suggestion);
	}


	function get_customers_suggestions()
	{
		$key = $_REQUEST["q"];
		$suggestion = array();

		$customers = $this->Customer_model->get_customers_suggestion($key);

		foreach ($customers as $customer) {
			$suggestion[] = array("id" => $customer->name, "name" => $customer->name);
		}

		$suggestion[] = array("id" => "+", "name" => "+ " . "create_new_item");

		echo json_encode($suggestion);
	}

	public function updatenew($id = 0)
	{

		$view_data['customer'] = $this->Common_model->get_one('clients', $id);
		$id = $this->input->post('customerid');

		$customerdata = array(
			'phone' => $this->input->post('phone'),
			'clienttype' => $this->input->post('clienttype'),
			'address' => $this->input->post('address'),
			'email' => $this->input->post('email'),
			$this->input->post('id') ? 'datemodified' : 'datecreated' => date('Y-m-d H:i:s'),
			$this->input->post('id') ? 'modifiedby' : 'createdby' => $this->session->userdata('id')

		);
		$data = $this->security->xss_clean($customerdata);
		$customerobj = $this->Customer_model->get_customer_by_id($id);
		if ($this->Customer_model->updatecustomer($customerdata, $id)) {
			echo json_encode($customerdata);
		} else {

			$this->session->set_flashdata('errors', 'error occured');
			$return = array('status' => 'error', 'message' => 'failed to update');
			echo json_encode($return);
		}
	}
}
