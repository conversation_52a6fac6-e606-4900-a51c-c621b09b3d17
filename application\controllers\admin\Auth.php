<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Auth extends MY_Controller {

	public function __construct(){

		parent::__construct();
		$this->load->model('admin/auth_model', 'auth_model');
	}

	//--------------------------------------------------------------
	public function index(){

		if($this->session->has_userdata('is_admin_login')){
			redirect('admin/dashboard');
		}
		else{
			redirect('admin/auth/login');
		}
	}

	//--------------------------------------------------------------
	public function login(){

		if($this->input->post('username') || $this->input->post('submit')){

			$this->form_validation->set_rules('username', 'Username', 'trim|required');
			$this->form_validation->set_rules('password', 'Password', 'trim|required');

			// Check if this is an AJAX request
			$is_ajax = $this->input->is_ajax_request();

			if ($this->form_validation->run() == FALSE) {
				if ($is_ajax) {
					// Return JSON response for AJAX
					$errors = array();
					if (form_error('username')) {
						$errors['username'] = strip_tags(form_error('username'));
					}
					if (form_error('password')) {
						$errors['password'] = strip_tags(form_error('password'));
					}

					echo json_encode([
						'success' => false,
						'message' => 'Please correct the errors below.',
						'errors' => $errors
					]);
					return;
				} else {
					// Regular form submission
					$data = array(
						'errors' => validation_errors()
					);
					$this->session->set_flashdata('error', $data['errors']);
					redirect(base_url('admin/auth/login'),'refresh');
				}
			}
			else {
				$data = array(
					'username' => $this->input->post('username'),
					'password' => $this->input->post('password'),
				);

				$result = $this->auth_model->login($data);

				if($result){

					if($result['is_active'] == 0){
						if ($is_ajax) {
							echo json_encode([
								'success' => false,
								'message' => 'Account is disabled. Please contact administrator!'
							]);
							return;
						} else {
							$this->session->set_flashdata('error', 'Account is disabled please contact admin!');
							redirect(base_url('admin/auth/login'));
							exit();
						}
					}

					if($result['is_admin'] == 1){
						$admin_data = array(
							'id' => $result['admin_id'],
							'admin_id' => $result['admin_id'],
							'username' => $result['username'],
							'admin_role_id' => $result['admin_role_id'],
							'admin_role' => $result['admin_role_title'],
							'is_supper' => $result['is_supper'],
							'staffid' => $result['staffid'],
							'is_admin_login' => TRUE
						);

						$this->session->set_userdata($admin_data);
						$this->rbac->set_access_in_session(); // set access in session

						if ($is_ajax) {
							echo json_encode([
								'success' => true,
								'message' => 'Login successful! Redirecting to dashboard...',
								'redirect_url' => base_url('admin/dashboard')
							]);
							return;
						} else {
							redirect(base_url('admin/dashboard'), 'refresh');
						}
					} else {
						if ($is_ajax) {
							echo json_encode([
								'success' => false,
								'message' => 'Access denied. Admin privileges required!'
							]);
							return;
						} else {
							$this->session->set_flashdata('error', 'Access denied. Admin privileges required!');
							redirect(base_url('admin/auth/login'));
						}
					}
				}
				else{
					if ($is_ajax) {
						echo json_encode([
							'success' => false,
							'message' => 'Invalid username or password!'
						]);
						return;
					} else {
						$this->session->set_flashdata('error', 'Invalid Username or Password!');
						redirect(base_url('admin/auth/login'));
					}
				}
			}
		}
		else{
			$data['title'] = 'Login';
			$this->load->view('components/header', $data);
			$this->load->view('admin/auth/login');
			$this->load->view('components/footer', $data);
		}
		}	

	
		//----------------------------------------------------------	
		public function verify(){

			$verification_id = $this->uri->segment(4);
			$result = $this->auth_model->email_verification($verification_id);
			if($result){
				$this->session->set_flashdata('success', 'Your email has been verified, you can now login.');
				redirect(base_url('admin/auth/login'));
			}
			else{
				$this->session->set_flashdata('success', 'The url is either invalid or you already have activated your account.');	
				redirect(base_url('admin/auth/login'));
			}	
		}

		

		//--------------------------------------------------		
		public function forgot_password(){

			if($this->input->post('submit')){
				//checking server side validation
				$this->form_validation->set_rules('email', 'Email', 'valid_email|trim|required');
				if ($this->form_validation->run() == FALSE) {
					$data = array(
						'errors' => validation_errors()
					);
					$this->session->set_flashdata('error', $data['errors']);
					redirect(base_url('admin/auth/forget_password'),'refresh');
				}

				$email = $this->input->post('email');
				$response = $this->auth_model->check_user_mail($email);

				if($response){

					$rand_no = rand(0,1000);
					$pwd_reset_code = md5($rand_no.$response['admin_id']);
					$this->auth_model->update_reset_code($pwd_reset_code, $response['admin_id']);
					
					// --- sending email
					$to = $response['email'];
					$mail_data= array(
						'fullname' => $response['firstname'].' '.$response['lastname'],
						'reset_link' => base_url('admin/auth/reset_password/'.$pwd_reset_code)
					);
					$this->mailer->mail_template($to,'forget-password',$mail_data);

					if($email){
						$this->session->set_flashdata('success', 'We have sent instructions for resetting your password to your email');

						redirect(base_url('admin/auth/forgot_password'));
					}
					else{
						$this->session->set_flashdata('error', 'There is the problem on your email');
						redirect(base_url('admin/auth/forgot_password'));
					}
				}
				else{
					$this->session->set_flashdata('error', 'The Email that you provided are invalid');
					redirect(base_url('admin/auth/forgot_password'));
				}
			}
			else{

				$data['title'] = 'Forget Password';
				$data['navbar'] = false;
				$data['sidebar'] = false;
				$data['footer'] = false;
				$data['bg_cover'] = true;

				$this->load->view('components/header_minimal', $data);
				$this->load->view('admin/auth/forget_password');
				$this->load->view('components/footer_minimal', $data);
			}
		}

		//----------------------------------------------------------------		
		public function reset_password($id=0){

			// check the activation code in database
			if($this->input->post('submit')){
				$this->form_validation->set_rules('password', 'Password', 'trim|required|min_length[5]');
				$this->form_validation->set_rules('confirm_password', 'Password Confirmation', 'trim|required|matches[password]');

				if ($this->form_validation->run() == FALSE) {
					$data = array(
						'errors' => validation_errors()
					);

					$this->session->set_flashdata('reset_code', $id);
					$this->session->set_flashdata('error', $data['errors']);
					redirect($_SERVER['HTTP_REFERER'], 'refresh');
				}
  
				else{
					$new_password = password_hash($this->input->post('password'), PASSWORD_BCRYPT);
					$this->auth_model->reset_password($id, $new_password);
					$this->session->set_flashdata('success','New password has been Updated successfully.Please login below');
					redirect(base_url('admin/auth/login'));
				}
			}
			else{
				$result = $this->auth_model->check_password_reset_code($id);

				if($result){

					$data['title'] = 'Reseat Password';
					$data['reset_code'] = $id;
					$data['navbar'] = false;
					$data['sidebar'] = false;
					$data['footer'] = false;
					$data['bg_cover'] = true;

					$this->load->view('components/header_minimal', $data);
					$this->load->view('admin/auth/reset_password');
					$this->load->view('components/footer_minimal', $data);

				}
				else{
					$this->session->set_flashdata('error','Password Reset Code is either invalid or expired.');
					redirect(base_url('admin/auth/forgot_password'));
				}
			}
		}

			public function changepassword($id=0){

			if($this->input->post('submit')){
				if ($this->input->post('staffid')) {
					$id = $this->input->post('staffid');
				}

				$this->form_validation->set_rules('password', 'Password', 'trim|required|min_length[5]');
				$this->form_validation->set_rules('confirmpassword', 'Password Confirmation', 'trim|required|matches[password]');

				if ($this->form_validation->run() == FALSE) {
					$data = array(
						'errors' => validation_errors()
					);

					$this->session->set_flashdata('error', $data['errors']);
					redirect($_SERVER['HTTP_REFERER']);
				}
  
				else{

					$new_password = password_hash($this->input->post('password'), PASSWORD_BCRYPT);
					$this->auth_model->changepassword($id, $new_password);
					$this->session->set_flashdata('success','New password has been Updated successfully');
					redirect($_SERVER['HTTP_REFERER']);
				}
			}
			else{
				$this->session->set_flashdata('warning','Nothing Happened');
				redirect($_SERVER['HTTP_REFERER']);
			}
		}

		public function logout(){
			$this->session->sess_destroy();
			redirect(base_url('admin/auth/login'), 'refresh');
		}
		
		// Get Country. State and City
		//----------------------------------------
		public function get_country_states()
		{
			$states = $this->db->select('*')->where('country_id',$this->input->post('country'))->get('states')->result_array();
		    $options = array('' => 'Select Option') + array_column($states,'name','id');
		    $html = form_dropdown('state',$options,'','class="form-control select2" required');
			$error =  array('msg' => $html);
			echo json_encode($error);
		}

		//----------------------------------------
		public function get_state_cities()
		{
			$cities = $this->db->select('*')->where('state_id',$this->input->post('state'))->get('cities')->result_array();
		    $options = array('' => 'Select Option') + array_column($cities,'name','id');
		    $html = form_dropdown('city',$options,'','class="form-control select2" required');
			$error =  array('msg' => $html);
			echo json_encode($error);
		}

	}  // end class


?>
