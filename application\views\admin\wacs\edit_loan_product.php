<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="content-wrapper">
	<div class="card">
		<div class="card-body">
			<?php $this->load->view('admin/includes/_messages.php') ?>
			<div class="row justify-content-center">
				<div class="col-md-6">
					<h2>Edit Loan Product</h2>
					<form id="editLoanProductForm" action="<?php echo base_url('admin/wacs/updateLoanProduct/' . $product['product_id']); ?>" method="post" class="needs-validation" novalidate>
						<?php echo form_hidden($this->security->get_csrf_token_name(), $this->security->get_csrf_hash()); ?>
						<div class="form-group">
							<label for="product_id">Product ID*</label>
							<input type="text" class="form-control" name="product_id" id="product_id" value="<?php echo htmlspecialchars($product['product_id']); ?>" required readonly>
							<div class="invalid-feedback">Product ID is required.</div>
						</div>
						<div class="form-group">
							<label for="title">Title*</label>
							<input type="text" class="form-control" name="title" id="title" value="<?php echo htmlspecialchars($product['title']); ?>" required>
							<div class="invalid-feedback">Title is required.</div>
						</div>
						<div class="form-group">
							<label for="description">Description*</label>
							<textarea class="form-control" name="description" id="description" required><?php echo htmlspecialchars($product['description']); ?></textarea>
							<div class="invalid-feedback">Description is required.</div>
						</div>
						<div class="form-group">
							<label for="product_image">Product Image URL</label>
							<input type="url" class="form-control" name="product_image" id="product_image" value="<?php echo htmlspecialchars($product['product_image']); ?>">
							<div class="invalid-feedback">Please provide a valid URL.</div>
						</div>
						<div class="form-group">
							<label for="category">Category*</label>
							<input type="text" class="form-control" name="category" id="category" value="<?php echo htmlspecialchars($product['category']); ?>" required>
							<div class="invalid-feedback">Category is required.</div>
						</div>
						<div class="form-group">
							<label for="feature">Features (comma separated)</label>
							<input type="text" class="form-control" name="feature" id="feature" value="<?php echo htmlspecialchars(is_array($product['feature']) ? implode(', ', $product['feature']) : $product['feature']); ?>">
						</div>
						<div class="form-group">
							<label for="interest_rate">Interest Rate (%)*</label>
							<input type="number" step="0.01" min="0" max="100" class="form-control" name="interest_rate" id="interest_rate" value="<?php echo htmlspecialchars($product['interest_rate']); ?>" required>
							<div class="invalid-feedback">Interest rate must be between 0 and 100.</div>
						</div>
						<div class="form-group">
							<label for="amount_from">Amount From*</label>
							<input type="number" min="0" class="form-control" name="amount_from" id="amount_from" value="<?php echo htmlspecialchars($product['amount_from']); ?>" required>
							<div class="invalid-feedback">Minimum amount is required.</div>
						</div>
						<div class="form-group">
							<label for="amount_to">Amount To*</label>
							<input type="number" min="0" class="form-control" name="amount_to" id="amount_to" value="<?php echo htmlspecialchars($product['amount_to']); ?>" required>
							<div class="invalid-feedback">Maximum amount is required and must be greater than minimum.</div>
						</div>
						<div class="form-group">
							<label for="moratorium_period">Moratorium Period (months)*</label>
							<input type="number" min="0" class="form-control" name="moratorium_period" id="moratorium_period" value="<?php echo htmlspecialchars($product['moratorium_period']); ?>" required>
							<div class="invalid-feedback">Moratorium period is required.</div>
						</div>
						<div class="form-group">
							<label for="payback_period">Payback Period (months)*</label>
							<input type="number" min="1" class="form-control" name="payback_period" id="payback_period" value="<?php echo htmlspecialchars($product['payback_period']); ?>" required>
							<div class="invalid-feedback">Payback period is required.</div>
						</div>
						<div class="form-group">
							<label for="bank_charge_type">Bank Charge Type*</label>
							<select class="form-control" name="bank_charge_type" id="bank_charge_type" required>
								<option value="">Select charge type</option>
								<option value="0" <?php echo $product['bank_charge_type'] == 0 ? 'selected' : ''; ?>>Flat</option>
								<option value="1" <?php echo $product['bank_charge_type'] == 1 ? 'selected' : ''; ?>>Percentage</option>
							</select>
							<div class="invalid-feedback">Charge type is required.</div>
						</div>
						<div class="form-group">
							<label for="bank_charge_value">Bank Charge Value*</label>
							<input type="number" min="0" step="0.01" class="form-control" name="bank_charge_value" id="bank_charge_value" value="<?php echo htmlspecialchars($product['bank_charge_value']); ?>" required>
							<div class="invalid-feedback">Charge value is required.</div>
						</div>
						<button type="submit" class="btn btn-primary mt-2">Update Loan Product</button>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
(function() {
	'use strict';
	var forms = document.querySelectorAll('.needs-validation');
	Array.prototype.slice.call(forms).forEach(function(form) {
		form.addEventListener('submit', function(event) {
			if (!form.checkValidity()) {
				event.preventDefault();
				event.stopPropagation();
			}
			form.classList.add('was-validated');
		}, false);
	});
	// Custom validations
	document.getElementById('amount_to').addEventListener('change', function() {
		var amountFrom = parseFloat(document.getElementById('amount_from').value);
		var amountTo = parseFloat(this.value);
		if (amountTo <= amountFrom) {
			this.setCustomValidity('Amount To must be greater than Amount From');
		} else {
			this.setCustomValidity('');
		}
	});
	document.getElementById('interest_rate').addEventListener('input', function() {
		var value = parseFloat(this.value);
		if (value < 0 || value > 100) {
			this.setCustomValidity('Interest rate must be between 0 and 100');
		} else {
			this.setCustomValidity('');
		}
	});
	document.getElementById('bank_charge_value').addEventListener('input', function() {
		var chargeType = document.getElementById('bank_charge_type').value;
		var value = parseFloat(this.value);
		if (chargeType === '1' && value > 100) {
			this.setCustomValidity('Percentage charge cannot exceed 100%');
		} else {
			this.setCustomValidity('');
		}
	});
})();
</script>
