<table class="table table-bordered table-striped">
    <thead>
        <tr>
            <th>IPPIS Number</th>
            <th>Full Name</th>
            <th>Phone</th>
            <th>Email</th>
            <th>MDA</th>
            <th>Current Salary</th>
            <th>Eligibility</th>
            <th>Bank</th>
            <th>Status</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
    <?php if (!empty($customers)) : foreach ($customers as $c) : ?>
        <tr>
            <td><?= htmlspecialchars($c['ippis_number']) ?></td>
            <td><?= htmlspecialchars($c['user_first_name'] . ' ' . $c['user_last_name']) ?></td>
            <td><?= htmlspecialchars($c['user_phone_number'] ?: $c['mobile_number'] ?: 'N/A') ?></td>
            <td><?= htmlspecialchars($c['user_email'] ?: $c['email_address'] ?: 'N/A') ?></td>
            <td><?= htmlspecialchars($c['mda']) ?></td>
            <td>₦<?= number_format($c['current_salary'] ?: 0, 2) ?></td>
            <td><b>₦<?= number_format($c['eligibility'] ?: 0, 2) ?></b></td>
            <td><?= htmlspecialchars($c['bank']) ?></td>
            <td>
                <span class="badge badge-<?= $c['employee_status'] == 'Active' ? 'success' : 'warning' ?>">
                    <?= htmlspecialchars($c['employee_status'] ?: 'Unknown') ?>
                </span>
            </td>
            <td>
                <div class="btn-group-vertical btn-group-sm" role="group">
                    <button type="button" class="btn btn-primary btn-sm mb-1" onclick="viewCustomerDetails(<?= $c['id'] ?>)">
                        <i class="fas fa-eye"></i> View Details
                    </button>
                    <a href="<?= base_url('admin/wacs/createLoanForm?ippis_number=' . urlencode($c['ippis_number'])) ?>" class="btn btn-success btn-sm mb-1">
                        <i class="fas fa-plus"></i> Create Loan
                    </a>
                    <a href="<?= base_url('admin/wacs/check_eligibility?ippis_number=' . urlencode($c['ippis_number'])) ?>" class="btn btn-info btn-sm mb-1">
                        <i class="fas fa-calculator"></i> Check Eligibility
                    </a>
                </div>
            </td>
        </tr>
    <?php endforeach; else: ?>
        <tr><td colspan="10" class="text-center">No onboarded customers found.</td></tr>
    <?php endif; ?>
    </tbody>
</table>
<div class="d-flex justify-content-between align-items-center mt-2">
    <div><b>Total:</b> <?= $total ?></div>
    <div><?= $pagination ?></div>
</div>

<!-- Customer Details Modal -->
<div class="modal fade" id="customerDetailsModal" tabindex="-1" role="dialog" aria-labelledby="customerDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerDetailsModalLabel">
                    <i class="fas fa-user"></i> Customer Details
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="customerDetailsContent">
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p class="mt-2">Loading customer details...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function viewCustomerDetails(customerId) {
    $('#customerDetailsModal').modal('show');
    $('#customerDetailsContent').html(`
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">Loading customer details...</p>
        </div>
    `);

    $.ajax({
        url: '<?= base_url('admin/wacs/getCustomerDetails') ?>/' + customerId,
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success && response.customer) {
                displayCustomerDetails(response.customer);
            } else {
                $('#customerDetailsContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${response.message || 'Failed to load customer details.'}
                    </div>
                `);
            }
        },
        error: function() {
            $('#customerDetailsContent').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    An error occurred while loading customer details.
                </div>
            `);
        }
    });
}

function displayCustomerDetails(customer) {
    var html = `
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user"></i> Personal Information</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>IPPIS Number:</strong></td><td>${customer.ippis_number || 'N/A'}</td></tr>
                            <tr><td><strong>Full Name:</strong></td><td>${customer.full_name || (customer.user_first_name + ' ' + customer.user_last_name)}</td></tr>
                            <tr><td><strong>Title:</strong></td><td>${customer.title || 'N/A'}</td></tr>
                            <tr><td><strong>Gender:</strong></td><td>${customer.more_info_gender || customer.gender || 'N/A'}</td></tr>
                            <tr><td><strong>Date of Birth:</strong></td><td>${customer.date_of_birth || 'N/A'}</td></tr>
                            <tr><td><strong>Marital Status:</strong></td><td>${customer.marital_status || 'N/A'}</td></tr>
                            <tr><td><strong>Nationality:</strong></td><td>${customer.more_info_nationality || customer.nationality || 'N/A'}</td></tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-envelope"></i> Contact Information</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Email:</strong></td><td>${customer.user_email || customer.email_address || 'N/A'}</td></tr>
                            <tr><td><strong>Phone:</strong></td><td>${customer.user_phone_number || customer.mobile_number || 'N/A'}</td></tr>
                            <tr><td><strong>Address:</strong></td><td>${customer.residential_address || customer.address || 'N/A'}</td></tr>
                            <tr><td><strong>State:</strong></td><td>${customer.residential_state || customer.state || 'N/A'}</td></tr>
                            <tr><td><strong>LGA:</strong></td><td>${customer.residential_lga || 'N/A'}</td></tr>
                            <tr><td><strong>City:</strong></td><td>${customer.residential_city || 'N/A'}</td></tr>
                            <tr><td><strong>Country:</strong></td><td>${customer.residential_country || 'N/A'}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-briefcase"></i> Employment Information</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>MDA:</strong></td><td>${customer.mda || 'N/A'}</td></tr>
                            <tr><td><strong>Department:</strong></td><td>${customer.department || 'N/A'}</td></tr>
                            <tr><td><strong>Rank:</strong></td><td>${customer.rank || 'N/A'}</td></tr>
                            <tr><td><strong>Cadre:</strong></td><td>${customer.cadre || 'N/A'}</td></tr>
                            <tr><td><strong>Grade Level:</strong></td><td>${customer.grade_level || 'N/A'}</td></tr>
                            <tr><td><strong>Grade Step:</strong></td><td>${customer.grade_step || 'N/A'}</td></tr>
                            <tr><td><strong>Current Salary:</strong></td><td>₦${parseFloat(customer.current_salary || 0).toLocaleString()}</td></tr>
                            <tr><td><strong>Employment Status:</strong></td><td><span class="badge badge-${customer.employee_status == 'Active' ? 'success' : 'warning'}">${customer.employee_status || 'Unknown'}</span></td></tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-university"></i> Banking Information</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Bank:</strong></td><td>${customer.bank || customer.bank_name || 'N/A'}</td></tr>
                            <tr><td><strong>Account Number:</strong></td><td>${customer.account_number || customer.more_info_account_number || 'N/A'}</td></tr>
                            <tr><td><strong>Account Name:</strong></td><td>${customer.account_name || 'N/A'}</td></tr>
                            <tr><td><strong>Bank Code:</strong></td><td>${customer.bank_code || customer.more_info_bank_code || 'N/A'}</td></tr>
                            <tr><td><strong>BVN:</strong></td><td>${customer.bvn || 'N/A'}</td></tr>
                            <tr><td><strong>Account Type:</strong></td><td>${customer.account_type || 'N/A'}</td></tr>
                            <tr><td><strong>PFA Name:</strong></td><td>${customer.pfa_name || customer.more_info_pfa_name || 'N/A'}</td></tr>
                            <tr><td><strong>PFA PIN:</strong></td><td>${customer.pfa_pin || 'N/A'}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-money-bill-wave"></i> Loan Eligibility Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="info-box">
                                    <span class="info-box-icon bg-success"><i class="fas fa-dollar-sign"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Current Eligibility</span>
                                        <span class="info-box-number">₦${parseFloat(customer.eligibility || 0).toLocaleString()}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-box">
                                    <span class="info-box-icon bg-info"><i class="fas fa-chart-line"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">More Info Eligibility</span>
                                        <span class="info-box-number">₦${parseFloat(customer.more_info_eligibility || 0).toLocaleString()}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-box">
                                    <span class="info-box-icon bg-warning"><i class="fas fa-calendar"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Date Registered</span>
                                        <span class="info-box-number">${customer.created_at ? new Date(customer.created_at).toLocaleDateString() : 'N/A'}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#customerDetailsContent').html(html);
}
</script>
