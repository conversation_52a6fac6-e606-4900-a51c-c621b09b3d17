  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0">
              <i class="fas fa-tachometer-alt text-primary"></i>
              Welcome, <span class="text-primary"><?php echo $this->session->userdata('username') ?></span>
            </h1>
            <p class="text-muted mb-0">Manage your financial services and customer operations</p>
          </div><!-- /.col -->
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="#" class="text-primary"><?= trans('home') ?></a></li>
              <li class="breadcrumb-item active">Dashboard</li>
            </ol>
          </div><!-- /.col -->
        </div><!-- /.row -->
      </div><!-- /.container-fluid -->
    </div>
    <!-- /.content-header -->

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">

        <!-- Dashboard Statistics -->
        <div class="row mb-4">
          <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
              <div class="inner">
                <h3 id="totalCustomers">0</h3>
                <p>Total Customers</p>
              </div>
              <div class="icon">
                <i class="fas fa-users"></i>
              </div>
              <a href="<?= base_url('admin/customers') ?>" class="small-box-footer">
                More info <i class="fas fa-arrow-circle-right"></i>
              </a>
            </div>
          </div>

          <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
              <div class="inner">
                <h3 id="activeLoans">0</h3>
                <p>Active Loans</p>
              </div>
              <div class="icon">
                <i class="fas fa-hand-holding-usd"></i>
              </div>
              <a href="<?= base_url('admin/loans') ?>" class="small-box-footer">
                More info <i class="fas fa-arrow-circle-right"></i>
              </a>
            </div>
          </div>

          <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
              <div class="inner">
                <h3 id="pendingApplications">0</h3>
                <p>Pending Applications</p>
              </div>
              <div class="icon">
                <i class="fas fa-clock"></i>
              </div>
              <a href="<?= base_url('admin/loans/pending') ?>" class="small-box-footer">
                More info <i class="fas fa-arrow-circle-right"></i>
              </a>
            </div>
          </div>

          <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
              <div class="inner">
                <h3 id="totalDisbursed">₦0</h3>
                <p>Total Disbursed</p>
              </div>
              <div class="icon">
                <i class="fas fa-money-bill-wave"></i>
              </div>
              <a href="<?= base_url('admin/reports/disbursements') ?>" class="small-box-footer">
                More info <i class="fas fa-arrow-circle-right"></i>
              </a>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="card card-primary card-outline">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-bolt"></i> Quick Actions
                </h3>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                    <a href="<?= base_url('admin/wacs/createLoanForm') ?>" class="btn btn-app bg-primary">
                      <i class="fas fa-plus"></i> New Loan
                    </a>
                  </div>
                  <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                    <a href="<?= base_url('admin/wacs/createWacsCustomerForm') ?>" class="btn btn-app bg-success">
                      <i class="fas fa-user-plus"></i> Add Customer
                    </a>
                  </div>
                  <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                    <a href="<?= base_url('admin/wacs/checkEligibilityForm') ?>" class="btn btn-app bg-warning">
                      <i class="fas fa-calculator"></i> Check Eligibility
                    </a>
                  </div>
                  <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                    <a href="<?= base_url('admin/wacs/listOnboardedCustomers') ?>" class="btn btn-app bg-info">
                      <i class="fas fa-list"></i> View Customers
                    </a>
                  </div>
                  <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                    <a href="<?= base_url('admin/reports') ?>" class="btn btn-app bg-secondary">
                      <i class="fas fa-chart-bar"></i> Reports
                    </a>
                  </div>
                  <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                    <a href="<?= base_url('admin/settings') ?>" class="btn btn-app bg-dark">
                      <i class="fas fa-cog"></i> Settings
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Main row -->
        <div class="row">
          <!-- Left col -->
          <div class="col-md-8">
          

            <!-- TABLE: RECENT CUSTOMERS -->
            <div class="card card-primary card-outline">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-users"></i> Recent Customers
                </h3>
                <div class="card-tools">
                  <a href="<?= base_url('admin/customers') ?>" class="btn btn-sm btn-primary">
                    <i class="fas fa-eye"></i> View All
                  </a>
                  <button type="button" class="btn btn-tool" data-widget="collapse">
                    <i class="fas fa-minus"></i>
                  </button>
                </div>
              </div>
              <!-- /.card-header -->
              <div class="card-body p-0">
                <div class="table-responsive">
                  <table class="table m-0">
                    <thead>
                    <tr>
                      <th>Name</th>
                      <th>Phone</th>
                      <th>Salary</th>
                      <th>Checked On</th>
                    </tr>
                    </thead>
                    <tbody>
                      <?php foreach ($customers as $customer): ?>
                        <?php 
                            $no_of_searchs = countwhere('customer_successful_search',['customerid'=>$customer->remitacustomerid]); 
                             $salary = !empty(getby(['phone'=>$customer->phone],'customer_salary_loan')) ? getby(['phone'=>$customer->phone],'customer_salary_loan') : null;
                             $sal = !empty($salary) ? json_decode($salary->salary) : null;
                        ?>
                        <tr>
                          <td><a href="<?php echo base_url('admin/customers/salary_loan_details/'.$customer->phone) ?>" ><?php echo $customer->fullname ?></a><br><small><b>Office:</b> <?php echo $customer->organization ?></small></td>
                          <td><?php echo $customer->phone ?></td>
                          <td><?php echo !empty($sal) ? formatMoney($sal[0]->amount) : '<span class="badge badge-danger"> no data </span>'; ?></td>
                          <td>
                            <?php echo formatDate($customer->lastchecked); ?> <span class="badge badge-warning"><?php echo $no_of_searchs ?> <i class="fa fa-search"></i></span> 
                            
                            <?php if ($customer->onremita == 1): ?>
                                <span class="badge badge-success">on remita</span>
                              <?php else: ?>
                                <span class="badge badge-danger">not on remita</span>
                            <?php endif ?>
                          </td>
                        </tr>
                      <?php endforeach ?>                    
                    </tbody>
                  </table>
                </div>
                <!-- /.table-responsive -->
              </div>
              <!-- /.card-body -->
              <div class="card-footer clearfix">
                <a href="<?php echo base_url('admin/customers/search') ?>" class="btn btn-sm btn-outline-primary float-left">New Search</a>
                <a href="<?php echo base_url('admin/customers') ?>" class="btn btn-sm btn-outline-secondary float-right">Customers</a>
              </div>
              <!-- /.card-footer -->
            </div>
            <!-- /.card -->
          </div>
          <!-- /.col -->

          <div class="col-md-4">
            <!-- SYSTEM STATUS -->
            <div class="card card-success card-outline">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-server"></i> System Status
                </h3>
              </div>
              <div class="card-body">
                <div class="info-box">
                  <span class="info-box-icon bg-success">
                    <i class="fas fa-check"></i>
                  </span>
                  <div class="info-box-content">
                    <span class="info-box-text">WACS API</span>
                    <span class="info-box-number">Online</span>
                  </div>
                </div>

                <div class="info-box">
                  <span class="info-box-icon bg-info">
                    <i class="fas fa-database"></i>
                  </span>
                  <div class="info-box-content">
                    <span class="info-box-text">Database</span>
                    <span class="info-box-number">Connected</span>
                  </div>
                </div>

                <div class="info-box">
                  <span class="info-box-icon bg-warning">
                    <i class="fas fa-clock"></i>
                  </span>
                  <div class="info-box-content">
                    <span class="info-box-text">Last Sync</span>
                    <span class="info-box-number" id="lastSync">--</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- RECENT ACTIVITIES -->
            <div class="card card-info card-outline">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-history"></i> Recent Activities
                </h3>
              </div>
              <div class="card-body p-0">
                <ul class="list-group list-group-flush" id="recentActivities">
                  <li class="list-group-item">
                    <i class="fas fa-user-plus text-success"></i>
                    New customer registered
                    <span class="float-right text-muted text-sm">2 mins ago</span>
                  </li>
                  <li class="list-group-item">
                    <i class="fas fa-hand-holding-usd text-primary"></i>
                    Loan application submitted
                    <span class="float-right text-muted text-sm">5 mins ago</span>
                  </li>
                  <li class="list-group-item">
                    <i class="fas fa-check-circle text-success"></i>
                    Loan approved
                    <span class="float-right text-muted text-sm">10 mins ago</span>
                  </li>
                  <li class="list-group-item">
                    <i class="fas fa-calculator text-warning"></i>
                    Eligibility checked
                    <span class="float-right text-muted text-sm">15 mins ago</span>
                  </li>
                </ul>
              </div>
              <div class="card-footer text-center">
                <a href="<?= base_url('admin/activities') ?>" class="btn btn-sm btn-outline-info">
                  View All Activities
                </a>
              </div>
            </div>
          </div>
          <!-- /.col -->
        </div>
        <!-- /.row -->
      </div><!--/. container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->


  <!-- PAGE PLUGINS -->
<!-- SparkLine -->
<script src="<?= base_url() ?>assets/plugins/sparkline/jquery.sparkline.min.js"></script>
<!-- jVectorMap -->
<script src="<?= base_url() ?>assets/plugins/jvectormap/jquery-jvectormap-1.2.2.min.js"></script>
<script src="<?= base_url() ?>assets/plugins/jvectormap/jquery-jvectormap-world-mill-en.js"></script>
<!-- SlimScroll 1.3.0 -->
<script src="<?= base_url() ?>assets/plugins/slimScroll/jquery.slimscroll.min.js"></script>
<!-- ChartJS 1.0.2 -->
<script src="<?= base_url() ?>assets/plugins/chartjs-old/Chart.min.js"></script>

<!-- PAGE SCRIPTS -->
<script>
$(document).ready(function() {
    // Load dashboard statistics
    loadDashboardStats();

    // Update last sync time
    updateLastSyncTime();

    // Refresh stats every 5 minutes
    setInterval(loadDashboardStats, 300000);

    function loadDashboardStats() {
        // You can implement AJAX calls to get real statistics
        // For now, using placeholder values

        // Simulate loading with animation
        animateCounter('#totalCustomers', 1247);
        animateCounter('#activeLoans', 89);
        animateCounter('#pendingApplications', 23);

        // Format currency for total disbursed
        setTimeout(function() {
            $('#totalDisbursed').text('₦45.2M');
        }, 1000);
    }

    function animateCounter(selector, targetValue) {
        var $element = $(selector);
        var currentValue = 0;
        var increment = targetValue / 50;

        var timer = setInterval(function() {
            currentValue += increment;
            if (currentValue >= targetValue) {
                currentValue = targetValue;
                clearInterval(timer);
            }
            $element.text(Math.floor(currentValue));
        }, 20);
    }

    function updateLastSyncTime() {
        var now = new Date();
        var timeString = now.toLocaleTimeString();
        $('#lastSync').text(timeString);
    }

    // Add hover effects to small boxes
    $('.small-box').hover(
        function() {
            $(this).addClass('elevation-3');
        },
        function() {
            $(this).removeClass('elevation-3');
        }
    );

    // Add click animation to quick action buttons
    $('.btn-app').on('click', function() {
        $(this).addClass('pulse');
        setTimeout(() => {
            $(this).removeClass('pulse');
        }, 300);
    });
});
</script>

<style>
.small-box {
    transition: all 0.3s ease;
}

.small-box:hover {
    transform: translateY(-2px);
}

.btn-app {
    transition: all 0.3s ease;
}

.btn-app:hover {
    transform: scale(1.05);
}

.pulse {
    animation: pulse 0.3s ease;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.info-box {
    transition: all 0.3s ease;
}

.info-box:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.list-group-item {
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}
</style>
