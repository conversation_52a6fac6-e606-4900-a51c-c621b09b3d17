<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WACS Customer Registration</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome removed -->
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --success-color: #059669;
            --danger-color: #dc2626;
            --warning-color: #d97706;
            --light-bg: #f8fafc;
            --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .registration-container {
            background: white;
            border-radius: 20px;
            box-shadow: var(--card-shadow);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header-section h1 {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .header-section p {
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .form-section {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            width: 100%;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            outline: none;
        }

        .form-control.is-invalid {
            border-color: var(--danger-color);
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
        }

        .invalid-feedback {
            color: var(--danger-color);
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: block;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 10px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }

        .btn-primary:disabled {
            opacity: 0.7;
            transform: none;
            box-shadow: none;
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }

        .alert-success {
            background: rgba(5, 150, 105, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .alert-danger {
            background: rgba(220, 38, 38, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .input-group {
            position: relative;
        }

        .input-group-text {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            z-index: 10;
            background: none;
            border: none;
        }

        .input-group .form-control {
            padding-left: 3rem;
        }

        .footer-section {
            background: var(--light-bg);
            padding: 1.5rem 2rem;
            text-align: center;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .success-animation {
            text-align: center;
            padding: 2rem;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: var(--success-color);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-bottom: 1rem;
            animation: bounceIn 0.6s ease-out;
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @media (max-width: 576px) {
            .registration-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header-section {
                padding: 1.5rem;
            }
            
            .form-section {
                padding: 1.5rem;
            }
            
            .header-section h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <div class="header-section">
            <h1>Consumer MFB WACS Registration</h1>
        </div>

        <div class="form-section">
            <form id="registrationForm" novalidate>
                <!-- CSRF Token -->
                <input type="hidden" name="<?php echo $this->security->get_csrf_token_name(); ?>" value="<?php echo $this->security->get_csrf_hash(); ?>" id="csrf_token">

                <div class="form-group">
                    <label for="ippis_number" class="form-label">
                        IPPIS Number
                    </label>
                    <div class="input-group">
                        <!-- icon removed -->
                        <input type="text" class="form-control" id="ippis_number" name="ippis_number" 
                               required pattern="[0-9]+" placeholder="Enter your IPPIS number">
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="bvn" class="form-label">
                        Bank Verification Number (BVN)
                    </label>
                    <div class="input-group">
                        <!-- icon removed -->
                        <input type="text" class="form-control" id="bvn" name="bvn" 
                               required pattern="[0-9]{11}" maxlength="11" placeholder="Enter your 11-digit BVN">
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="account_number" class="form-label">
                        Account Number
                    </label>
                    <div class="input-group">
                        <!-- icon removed -->
                        <input type="text" class="form-control" id="account_number" name="account_number" 
                               required pattern="[0-9]{10}" maxlength="10" placeholder="Enter your 10-digit account number">
                        <div class="invalid-feedback"></div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">
                    <span class="btn-text">
                        Register Now
                    </span>
                    <span class="btn-loading" style="display: none;">
                        <span class="loading-spinner"></span> Processing...
                    </span>
                </button>
            </form>

            <div id="resultMessage" style="display: none;"></div>
        </div>

        <div class="footer-section">
            <p>Your information is secure and encrypted</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        // CSRF Token Configuration
        const csrfName = '<?php echo $this->security->get_csrf_token_name(); ?>';
        let csrfHash = '<?php echo $this->security->get_csrf_hash(); ?>';

        // Function to update CSRF token in form
        function updateCSRFToken(newHash) {
            if (newHash) {
                csrfHash = newHash;
                $('#csrf_token').val(newHash);
            }
        }
    </script>

    <script>
        $(document).ready(function() {
            // Clear validation errors when user starts typing
            $('#registrationForm input').on('input', function() {
                $(this).removeClass('is-invalid');
                $(this).siblings('.invalid-feedback').text('');
                $('#resultMessage').hide();
            });

            // Form validation
            function validateForm() {
                let isValid = true;

                // Clear previous errors
                $('.form-control').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                // IPPIS Number validation
                const ippisNumber = $('#ippis_number').val().trim();
                if (!ippisNumber) {
                    $('#ippis_number').addClass('is-invalid');
                    $('#ippis_number').siblings('.invalid-feedback').text('IPPIS number is required.');
                    isValid = false;
                } else if (!/^[0-9]+$/.test(ippisNumber)) {
                    $('#ippis_number').addClass('is-invalid');
                    $('#ippis_number').siblings('.invalid-feedback').text('IPPIS number must contain only digits.');
                    isValid = false;
                }

                // BVN validation
                const bvn = $('#bvn').val().trim();
                if (!bvn) {
                    $('#bvn').addClass('is-invalid');
                    $('#bvn').siblings('.invalid-feedback').text('BVN is required.');
                    isValid = false;
                } else if (!/^[0-9]{11}$/.test(bvn)) {
                    $('#bvn').addClass('is-invalid');
                    $('#bvn').siblings('.invalid-feedback').text('BVN must be exactly 11 digits.');
                    isValid = false;
                }

                // Account Number validation
                const accountNumber = $('#account_number').val().trim();
                if (!accountNumber) {
                    $('#account_number').addClass('is-invalid');
                    $('#account_number').siblings('.invalid-feedback').text('Account number is required.');
                    isValid = false;
                } else if (!/^[0-9]{10}$/.test(accountNumber)) {
                    $('#account_number').addClass('is-invalid');
                    $('#account_number').siblings('.invalid-feedback').text('Account number must be exactly 10 digits.');
                    isValid = false;
                }

                return isValid;
            }

            // Form submission
            $('#registrationForm').on('submit', function(e) {
                e.preventDefault();

                if (!validateForm()) {
                    return;
                }

                const form = $(this);
                const submitBtn = form.find('button[type="submit"]');
                const btnText = submitBtn.find('.btn-text');
                const btnLoading = submitBtn.find('.btn-loading');

                // Show loading state
                submitBtn.prop('disabled', true);
                btnText.hide();
                btnLoading.show();

                // Prepare form data with CSRF token
                const formData = {
                    ippis_number: $('#ippis_number').val().trim(),
                    bvn: $('#bvn').val().trim(),
                    account_number: $('#account_number').val().trim()
                };

                // Add CSRF token
                formData[csrfName] = csrfHash;

                $.ajax({
                    url: '<?= base_url('public_wacs/register') ?>',
                    method: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        // Update CSRF token if provided in response
                        updateCSRFToken(response.csrf_hash);

                        if (response.success) {
                            showSuccessMessage(response.message || 'Registration successful! You are now registered in the WACS system.');

                            // Reset form on successful registration
                            $('#registrationForm')[0].reset();
                            $('.is-invalid').removeClass('is-invalid');
                            $('.invalid-feedback').text('');
                        } else {
                            showErrorMessage(response.message || 'Registration failed. Please try again.');
                        }
                    },
                    error: function(xhr) {
                        let errorMsg = 'An error occurred during registration. Please try again.';

                        // Check for CSRF token issues
                        if (xhr.status === 403) {
                            errorMsg = 'Security token expired. Please refresh the page and try again.';
                        }

                        if (xhr.responseJSON) {
                            if (xhr.responseJSON.errors && typeof xhr.responseJSON.errors === 'object') {
                                // Handle field-specific errors
                                Object.keys(xhr.responseJSON.errors).forEach(function(field) {
                                    const errorText = xhr.responseJSON.errors[field];
                                    const input = form.find('[name="' + field + '"]');
                                    if (input.length) {
                                        input.addClass('is-invalid');
                                        input.siblings('.invalid-feedback').text(errorText);
                                    }
                                });

                                if (xhr.responseJSON.message) {
                                    errorMsg = xhr.responseJSON.message;
                                }
                            } else if (xhr.responseJSON.message) {
                                errorMsg = xhr.responseJSON.message;
                            }
                        }

                        showErrorMessage(errorMsg);
                    },
                    complete: function() {
                        // Reset button state
                        submitBtn.prop('disabled', false);
                        btnText.show();
                        btnLoading.hide();
                    }
                });
            });

            function showSuccessMessage(message) {
                const successHtml = `
                    <div class="success-animation">
                        <div class="success-icon" style="font-size:2.5rem;line-height:80px;">&#10003;</div>
                        <h4 class="text-success mb-3">Registration Successful!</h4>
                        <p class="text-muted mb-4">${message}</p>
                        <button type="button" class="btn btn-primary" onclick="resetForm()">
                            Register Another Customer
                        </button>
                    </div>
                `;

                $('#resultMessage').html(successHtml).show();
                $('#registrationForm').hide();
            }

            function showErrorMessage(message) {
                const errorHtml = `
                    <div class="alert alert-danger">
                        <strong>Registration Failed:</strong> ${message}
                    </div>
                `;

                $('#resultMessage').html(errorHtml).show();
            }

            // Reset form function
            window.resetForm = function() {
                $('#registrationForm')[0].reset();
                $('.form-control').removeClass('is-invalid');
                $('.invalid-feedback').text('');
                $('#resultMessage').hide();
                $('#registrationForm').show();
                $('#ippis_number').focus();
            };

            // Auto-focus first input
            $('#ippis_number').focus();
        });
    </script>
