<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="content-wrapper">
    <!-- Content Header -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><i class="fas fa-tachometer-alt"></i> WACS Dashboard</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('admin') ?>">Home</a></li>
                        <li class="breadcrumb-item active">WACS Dashboard</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Stats Cards -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3><?php echo isset($stats['customers']) ? number_format($stats['customers']) : 0; ?></h3>
                            <p>Total Customers</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <a href="<?= base_url('admin/wacs/listOnboardedCustomers') ?>" class="small-box-footer">
                            More info <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3><?php echo isset($stats['loans']) ? number_format($stats['loans']) : 0; ?></h3>
                            <p>Active Loans</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <a href="<?= base_url('admin/wacs/allLoans') ?>" class="small-box-footer">
                            More info <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>₦<?php echo isset($stats['total_disbursed']) ? number_format($stats['total_disbursed'], 0) : '0'; ?></h3>
                            <p>Total Disbursed</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <a href="#" class="small-box-footer">
                            View Details <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>₦<?php echo isset($stats['total_repaid']) ? number_format($stats['total_repaid'], 0) : '0'; ?></h3>
                            <p>Total Repaid</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <a href="#" class="small-box-footer">
                            View Details <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- API Status -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title"><i class="fas fa-heartbeat"></i> WACS API Status</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-sm btn-primary" onclick="refreshApiStatus()">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="apiStatusContent">
                                <div class="text-center">
                                    <i class="fas fa-spinner fa-spin"></i> Checking API status...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title"><i class="fas fa-bolt"></i> Quick Actions</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('admin/wacs/createLoanForm') ?>" class="btn btn-app bg-success">
                                        <i class="fas fa-plus"></i> New Loan
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('admin/wacs/createWacsCustomerForm') ?>" class="btn btn-app bg-info">
                                        <i class="fas fa-user-plus"></i> Onboard Customer
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('admin/wacs/checkEligibilityForm') ?>" class="btn btn-app bg-warning">
                                        <i class="fas fa-calculator"></i> Check Eligibility
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('admin/wacs/createLoanProductForm') ?>" class="btn btn-app bg-primary">
                                        <i class="fas fa-cog"></i> Loan Product
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('admin/wacs/listLoanProducts') ?>" class="btn btn-app bg-secondary">
                                        <i class="fas fa-list"></i> All Products
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('admin/wacs/allLoans') ?>" class="btn btn-app bg-dark">
                                        <i class="fas fa-file-alt"></i> All Loans
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('admin/wacs/apiDiagnostics') ?>" class="btn btn-app bg-danger">
                                        <i class="fas fa-stethoscope"></i> API Diagnostics
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Recent Loans -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title"><i class="fas fa-clock"></i> Recent Loans</h3>
                            <div class="card-tools">
                                <div class="input-group input-group-sm" style="width: 250px;">
                                    <input type="text" id="loanSearch" class="form-control float-right" placeholder="Search by name, IPPIS, or account">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-default">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div id="recentLoansContainer">
                                <?php if (!empty($recent_loans)) : ?>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover mb-0">
                                            <thead class="thead-light">
                                                <tr>
                                                    <th>Loan ID</th>
                                                    <th>Customer</th>
                                                    <th>IPPIS</th>
                                                    <th>Product</th>
                                                    <th>Amount</th>
                                                    <th>Disbursed</th>
                                                    <th>Status</th>
                                                    <th>Date</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="loansTableBody">
                                                <?php foreach ($recent_loans as $loan) : ?>
                                                    <tr class="loan-row" data-search="<?= strtolower(htmlspecialchars($loan['employee_name'] ?? $loan['debtor'] ?? '') . ' ' . ($loan['customer_ippis'] ?? '') . ' ' . ($loan['account_number'] ?? '')) ?>">
                                                        <td>
                                                            <span class="badge badge-primary"><?= htmlspecialchars($loan['loan_id'] ?? $loan['id']) ?></span>
                                                        </td>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="user-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mr-2" style="width: 35px; height: 35px; font-size: 14px;">
                                                                    <?= strtoupper(substr($loan['employee_name'] ?? $loan['debtor'] ?? 'N', 0, 2)) ?>
                                                                </div>
                                                                <div>
                                                                    <div class="font-weight-bold"><?= htmlspecialchars($loan['employee_name'] ?? $loan['debtor'] ?? 'N/A') ?></div>
                                                                    <small class="text-muted"><?= htmlspecialchars($loan['customer_email'] ?? '') ?></small>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="font-weight-bold"><?= htmlspecialchars($loan['customer_ippis'] ?? 'N/A') ?></span>
                                                        </td>
                                                        <td>
                                                            <span class="text-info"><?= htmlspecialchars($loan['loan_product'] ?? 'N/A') ?></span>
                                                        </td>
                                                        <td>
                                                            <span class="font-weight-bold text-success">₦<?= number_format($loan['amount_requested'] ?? 0, 2) ?></span>
                                                        </td>
                                                        <td>
                                                            <span class="font-weight-bold text-primary">₦<?= number_format($loan['disbursed_amount'] ?? 0, 2) ?></span>
                                                        </td>
                                                        <td>
                                                            <?php
                                                            $status = $loan['status_loan'] ?? $loan['status'] ?? 'pending';
                                                            $badgeClass = $status === 'approved' ? 'success' : ($status === 'pending' ? 'warning' : ($status === 'rejected' ? 'danger' : 'info'));
                                                            ?>
                                                            <span class="badge badge-<?= $badgeClass ?> px-2 py-1">
                                                                <i class="fas fa-<?= $status === 'approved' ? 'check' : ($status === 'pending' ? 'clock' : ($status === 'rejected' ? 'times' : 'info')) ?>"></i>
                                                                <?= ucfirst(htmlspecialchars($status)) ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="text-muted"><?= date('M d, Y', strtotime($loan['start_date'] ?? 'now')) ?></span>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <button type="button" class="btn btn-sm btn-outline-primary loan-action-btn"
                                                                        data-loan-id="<?= htmlspecialchars($loan['loan_id'] ?? $loan['id']) ?>"
                                                                        data-customer-id="<?= htmlspecialchars($loan['customer_id'] ?? '') ?>"
                                                                        data-action="view" title="View Details">
                                                                    <i class="fas fa-eye"></i>
                                                                </button>
                                                                <?php
                                                                $loanStatus = strtolower($loan['status'] ?? '');
                                                                $isAwaitingDisbursal = $loanStatus === 'awaiting disbursal';
                                                                $isApproved = in_array($loanStatus, ['approved', 'awaiting disbursal', 'disbursed', 'active']);
                                                                ?>
                                                                <?php if (!$isAwaitingDisbursal): ?>
                                                                <button type="button" class="btn btn-sm btn-outline-success loan-action-btn"
                                                                        data-loan-id="<?= htmlspecialchars($loan['loan_id'] ?? $loan['id']) ?>"
                                                                        data-customer-id="<?= htmlspecialchars($loan['customer_id'] ?? '') ?>"
                                                                        data-action="approve" title="Approve">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                                <?php endif; ?>
                                                                <?php if (!$isApproved): ?>
                                                                <button type="button" class="btn btn-sm btn-outline-danger loan-action-btn"
                                                                        data-loan-id="<?= htmlspecialchars($loan['loan_id'] ?? $loan['id']) ?>"
                                                                        data-customer-id="<?= htmlspecialchars($loan['customer_id'] ?? '') ?>"
                                                                        data-action="reject" title="Reject">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                                <?php endif; ?>
                                                                <button type="button" class="btn btn-sm btn-outline-warning loan-action-btn"
                                                                        data-loan-id="<?= htmlspecialchars($loan['loan_id'] ?? $loan['id']) ?>"
                                                                        data-customer-id="<?= htmlspecialchars($loan['customer_id'] ?? '') ?>"
                                                                        data-action="liquidate" title="Liquidate">
                                                                    <i class="fas fa-money-bill"></i>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else : ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No recent loans found</h5>
                                        <p class="text-muted">Start by creating a new loan application</p>
                                        <a href="<?= base_url('admin/wacs/createLoanForm') ?>" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> Create New Loan
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php if (!empty($recent_loans)) : ?>
                        <div class="card-footer">
                            <div class="row">
                                <div class="col-sm-6">
                                    <small class="text-muted">Showing <?= count($recent_loans) ?> recent loans</small>
                                </div>
                                <div class="col-sm-6 text-right">
                                    <a href="<?= base_url('admin/wacs/allLoans') ?>" class="btn btn-sm btn-outline-primary">
                                        View All Loans <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
// Notification helper function using the existing notify.js library
function showNotification(type, message) {
    if (typeof $.notify !== 'undefined') {
        // Use the existing notify library
        var notifyType = 'info';
        var notifyIcon = 'fa fa-info-circle';

        switch(type) {
            case 'success':
                notifyType = 'success';
                notifyIcon = 'fa fa-check-circle';
                break;
            case 'error':
                notifyType = 'danger';
                notifyIcon = 'fa fa-exclamation-circle';
                break;
            case 'warning':
                notifyType = 'warning';
                notifyIcon = 'fa fa-exclamation-triangle';
                break;
            case 'info':
                notifyType = 'info';
                notifyIcon = 'fa fa-info-circle';
                break;
        }

        $.notify({
            icon: notifyIcon,
            message: message
        }, {
            type: notifyType,
            delay: 5000,
            placement: {
                from: "top",
                align: "right"
            },
            animate: {
                enter: 'animated fadeInDown',
                exit: 'animated fadeOutUp'
            }
        });
    } else {
        // Fallback to native alert
        alert(message);
    }
}
</script>

<style>
/* Enhanced notification styling */
.alert-notify {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border: none;
}

.alert-notify .close {
    font-size: 18px;
    opacity: 0.8;
}

.alert-notify .close:hover {
    opacity: 1;
}

/* Animation classes for notifications */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translate3d(0, -100%, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes fadeOutUp {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        transform: translate3d(0, -100%, 0);
    }
}

.animated {
    animation-duration: 0.5s;
    animation-fill-mode: both;
}

.fadeInDown {
    animation-name: fadeInDown;
}

.fadeOutUp {
    animation-name: fadeOutUp;
}
</style>

    <!-- Loan Details Modal -->
    <div class="modal fade" id="loanDetailsModal" tabindex="-1" role="dialog" aria-labelledby="loanDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="loanDetailsModalLabel">
                        <i class="fas fa-file-alt"></i> Loan Details
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="loanDetailsContent">
                    <div class="text-center py-4">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-2">Loading loan details...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loan Approval Modal -->
    <div class="modal fade" id="loanApprovalModal" tabindex="-1" role="dialog" aria-labelledby="loanApprovalModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="loanApprovalModalLabel">
                        <i class="fas fa-check-circle"></i> Approve Loan
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Confirm Approval:</strong> Are you sure you want to approve this loan? This action cannot be undone.
                    </div>
                    <div id="approvalLoanInfo"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="button" class="btn btn-success" id="confirmApproval">
                        <i class="fas fa-check"></i> Approve Loan
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loan Rejection Modal -->
    <div class="modal fade" id="loanRejectionModal" tabindex="-1" role="dialog" aria-labelledby="loanRejectionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="loanRejectionModalLabel">
                        <i class="fas fa-times-circle"></i> Reject Loan
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="loanRejectionForm">
                        <?php echo form_hidden($this->security->get_csrf_token_name(), $this->security->get_csrf_hash()); ?>
                        <input type="hidden" name="loan_id" id="rejection_loan_id">

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Confirm Rejection:</strong> Please provide a reason for rejecting this loan.
                        </div>

                        <div id="rejectionLoanInfo" class="mb-3"></div>

                        <div class="form-group">
                            <label for="rejection_reason"><i class="fas fa-comment"></i> Rejection Reason <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="rejection_reason" name="reason" rows="4" required placeholder="Please provide a detailed reason for rejecting this loan..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="submit" form="loanRejectionForm" class="btn btn-danger">
                        <i class="fas fa-times"></i> Reject Loan
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loan Liquidation Modal -->
    <div class="modal fade" id="loanLiquidationModal" tabindex="-1" role="dialog" aria-labelledby="loanLiquidationModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="loanLiquidationModalLabel">
                        <i class="fas fa-money-bill"></i> Liquidate Loan
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="loanLiquidationForm">
                        <?php echo form_hidden($this->security->get_csrf_token_name(), $this->security->get_csrf_hash()); ?>
                        <input type="hidden" name="loan_id" id="liquidation_loan_id">
                        <input type="hidden" name="customer_id" id="liquidation_customer_id">

                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Warning:</strong> Liquidating a loan will terminate it immediately. This action cannot be undone.
                        </div>

                        <div id="liquidationLoanInfo" class="mb-3"></div>

                        <div class="form-group">
                            <label for="liquidation_note"><i class="fas fa-sticky-note"></i> Liquidation Note <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="liquidation_note" name="note" rows="4" required placeholder="Please provide a detailed reason for liquidating this loan..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="submit" form="loanLiquidationForm" class="btn btn-warning">
                        <i class="fas fa-money-bill"></i> Liquidate Loan
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Global variables
    var currentLoanId = null;
    var currentCustomerId = null;

    // Search functionality
    $('#loanSearch').on('keyup', function() {
        var searchTerm = $(this).val().toLowerCase();
        $('#loansTableBody tr.loan-row').each(function() {
            var searchData = $(this).data('search');
            if (searchData.indexOf(searchTerm) > -1) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });

        // Show/hide "no results" message
        var visibleRows = $('#loansTableBody tr.loan-row:visible').length;
        if (visibleRows === 0 && searchTerm !== '') {
            if ($('#noResultsRow').length === 0) {
                $('#loansTableBody').append('<tr id="noResultsRow"><td colspan="9" class="text-center py-4"><i class="fas fa-search"></i> No loans found matching your search.</td></tr>');
            }
        } else {
            $('#noResultsRow').remove();
        }
    });

    // Loan action button handler
    $(document).on('click', '.loan-action-btn', function() {
        var loanId = $(this).data('loan-id');
        var customerId = $(this).data('customer-id');
        var action = $(this).data('action');

        currentLoanId = loanId;
        currentCustomerId = customerId;

        switch(action) {
            case 'view':
                showLoanDetails(loanId);
                break;
            case 'approve':
                showApprovalModal(loanId);
                break;
            case 'reject':
                showRejectionModal(loanId);
                break;
            case 'liquidate':
                showLiquidationModal(loanId, customerId);
                break;
        }
    });

    // Show loan details modal
    function showLoanDetails(loanId) {
        $('#loanDetailsModal').modal('show');
        $('#loanDetailsContent').html(`
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">Loading loan details...</p>
            </div>
        `);

        // Get loan details and application status
        Promise.all([
            $.ajax({
                url: '<?= base_url('admin/wacs/getLoanDetails') ?>/' + loanId,
                method: 'GET',
                dataType: 'json'
            }),
            getLoanApplicationStatus(loanId)
        ]).then(function(responses) {
            var loanDetailsResponse = responses[0];
            var applicationStatusResponse = responses[1];

            if (loanDetailsResponse.success && loanDetailsResponse.data && loanDetailsResponse.data.loan) {
                displayLoanDetails(loanDetailsResponse.data.loan, applicationStatusResponse);
            } else {
                $('#loanDetailsContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${loanDetailsResponse.message || 'Failed to load loan details.'}
                    </div>
                `);
            }
        }).catch(function() {
            $('#loanDetailsContent').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    An error occurred while loading loan details.
                </div>
            `);
        });
    }

    // Get loan application status by IPPIS number
    function getLoanApplicationStatus(loanId) {
        // First get the loan details to extract IPPIS number
        return $.ajax({
            url: '<?= base_url('admin/wacs/getLoanDetails') ?>/' + loanId,
            method: 'GET',
            dataType: 'json'
        }).then(function(response) {
            if (response.success && response.data && response.data.loan && response.data.loan.customerIppis) {
                var ippisNumber = response.data.loan.customerIppis;
                return $.ajax({
                    url: '<?= base_url('admin/wacs/getLoanApplicationStatus') ?>/' + ippisNumber,
                    method: 'GET',
                    dataType: 'json'
                });
            }
            return null;
        }).catch(function() {
            return null;
        });
    }

    // Display loan details
    function displayLoanDetails(loan, applicationStatusResponse) {
        var html = `
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-info-circle"></i> Loan Information</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr><td><strong>Loan ID:</strong></td><td>${loan.loanID || 'N/A'}</td></tr>
                                <tr><td><strong>Debtor:</strong></td><td>${loan.debtor || loan.employeeName || 'N/A'}</td></tr>
                                <tr><td><strong>IPPIS:</strong></td><td>${loan.customerIppis || 'N/A'}</td></tr>
                                <tr><td><strong>MDA:</strong></td><td>${loan.mda || 'N/A'}</td></tr>
                                <tr><td><strong>Product:</strong></td><td>${loan.loanProduct || 'N/A'}</td></tr>
                                <tr><td><strong>Category:</strong></td><td>${loan.loanProductCategory || 'N/A'}</td></tr>
                                <tr><td><strong>Status:</strong></td><td><span class="badge badge-info">${loan.status || 'N/A'}</span></td></tr>
                                <tr><td><strong>Creditor:</strong></td><td>${loan.creditor || 'N/A'}</td></tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-money-bill-wave"></i> Financial Details</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr><td><strong>Amount Requested:</strong></td><td>₦${parseFloat(loan.amountRequested || 0).toLocaleString()}</td></tr>
                                <tr><td><strong>Amount Offered:</strong></td><td>₦${parseFloat(loan.amountOffered || 0).toLocaleString()}</td></tr>
                                <tr><td><strong>Disbursed Amount:</strong></td><td>₦${parseFloat(loan.disbursedAmount || 0).toLocaleString()}</td></tr>
                                <tr><td><strong>Interest Rate:</strong></td><td>${loan.interestRate || 0}% ${loan.interestRateType || 'yearly'}</td></tr>
                                <tr><td><strong>Tenure:</strong></td><td>${loan.loanTenure || 0} months</td></tr>
                                <tr><td><strong>Moratorium:</strong></td><td>${loan.moratorium || 0} month(s)</td></tr>
                                <tr><td><strong>Balance:</strong></td><td>₦${parseFloat(loan.balance || 0).toLocaleString()}</td></tr>
                                <tr><td><strong>Amount Paid:</strong></td><td>₦${parseFloat(loan.amountPaidSoFar || 0).toLocaleString()}</td></tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-calendar-alt"></i> Repayment Breakdown</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="info-box bg-primary">
                                        <span class="info-box-icon"><i class="fas fa-money-bill"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Total Repayment</span>
                                            <span class="info-box-number">₦${parseFloat(loan.repaymentAmount || 0).toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="info-box bg-success">
                                        <span class="info-box-icon"><i class="fas fa-calendar"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Monthly Repayment</span>
                                            <span class="info-box-number">₦${parseFloat(loan.monthlyRepaymentAmount || 0).toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="info-box bg-warning">
                                        <span class="info-box-icon"><i class="fas fa-chart-line"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">WACS Monthly</span>
                                            <span class="info-box-number">₦${parseFloat(loan.monthlyWACSRepaymentAmount || 0).toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="info-box bg-info">
                                        <span class="info-box-icon"><i class="fas fa-university"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">MFB Repayment Amount</span>
                                            <span class="info-box-number">₦${parseFloat(loan.repaymentMFBAmount || 0).toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-box bg-secondary">
                                        <span class="info-box-icon"><i class="fas fa-building"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">WACS Repayment Amount</span>
                                            <span class="info-box-number">₦${parseFloat(loan.repaymentWACsAmount || 0).toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Start Date:</strong> ${loan.startDate ? new Date(loan.startDate).toLocaleDateString() : 'N/A'} |
                                        <strong>Created:</strong> ${loan.createdAt || 'N/A'}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add application status data if available
        if (applicationStatusResponse && applicationStatusResponse.success && applicationStatusResponse.data && applicationStatusResponse.data.loans) {
            var applicationLoans = applicationStatusResponse.data.loans;
            var currentLoan = applicationLoans.find(l => l.loanID === loan.loanID);

            if (currentLoan && currentLoan.customer) {
                html += `
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-user-circle"></i> Customer Application Status</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-sm">
                                                <tr><td><strong>Current Salary:</strong></td><td>₦${parseFloat(currentLoan.customer.current_salary || 0).toLocaleString()}</td></tr>
                                                <tr><td><strong>Current Eligibility:</strong></td><td>₦${parseFloat(currentLoan.customer.current_eligibility || 0).toLocaleString()}</td></tr>
                                                <tr><td><strong>Bank:</strong></td><td>${currentLoan.customer.bank || 'N/A'}</td></tr>
                                                <tr><td><strong>Account Number:</strong></td><td>${currentLoan.customer.account_number || 'N/A'}</td></tr>
                                                <tr><td><strong>BVN:</strong></td><td>${currentLoan.customer.bvn || 'N/A'}</td></tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-sm">
                                                <tr><td><strong>Employee Status:</strong></td><td><span class="badge badge-success">${currentLoan.customer.employee_status || 'N/A'}</span></td></tr>
                                                <tr><td><strong>Phone Verified:</strong></td><td>${currentLoan.customer.phone_number_verified_at ? new Date(currentLoan.customer.phone_number_verified_at).toLocaleDateString() : 'Not verified'}</td></tr>
                                                <tr><td><strong>State:</strong></td><td>${currentLoan.customer.state || 'N/A'}</td></tr>
                                                <tr><td><strong>Bank Code:</strong></td><td>${currentLoan.customer.bank_code || 'N/A'}</td></tr>
                                                <tr><td><strong>Last Updated:</strong></td><td>${currentLoan.customer.updated_at ? new Date(currentLoan.customer.updated_at).toLocaleDateString() : 'N/A'}</td></tr>
                                            </table>
                                        </div>
                                    </div>

                                    ${currentLoan.customer.user ? `
                                    <div class="row mt-3">
                                        <div class="col-md-12">
                                            <h6><i class="fas fa-user"></i> User Information</h6>
                                            <table class="table table-sm">
                                                <tr><td><strong>Full Name:</strong></td><td>${currentLoan.customer.user.first_name || ''} ${currentLoan.customer.user.middle_name || ''} ${currentLoan.customer.user.last_name || ''}</td></tr>
                                                <tr><td><strong>Email:</strong></td><td>${currentLoan.customer.user.email || 'N/A'}</td></tr>
                                                <tr><td><strong>Phone:</strong></td><td>${currentLoan.customer.user.phone_number || 'N/A'}</td></tr>
                                                <tr><td><strong>Role:</strong></td><td><span class="badge badge-info">${currentLoan.customer.user.role || 'N/A'}</span></td></tr>
                                                <tr><td><strong>Email Verified:</strong></td><td>${currentLoan.customer.user.email_verified_at ? new Date(currentLoan.customer.user.email_verified_at).toLocaleDateString() : 'Not verified'}</td></tr>
                                            </table>
                                        </div>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        $('#loanDetailsContent').html(html);
    }

    // Show approval modal
    function showApprovalModal(loanId) {
        $('#approvalLoanInfo').html(`<p><strong>Loan ID:</strong> ${loanId}</p>`);
        $('#loanApprovalModal').modal('show');
    }

    // Show rejection modal
    function showRejectionModal(loanId) {
        $('#rejection_loan_id').val(loanId);
        $('#rejectionLoanInfo').html(`<p><strong>Loan ID:</strong> ${loanId}</p>`);
        $('#loanRejectionModal').modal('show');
    }

    // Show liquidation modal
    function showLiquidationModal(loanId, customerId) {
        $('#liquidation_loan_id').val(loanId);
        $('#liquidation_customer_id').val(customerId);
        $('#liquidationLoanInfo').html(`
            <p><strong>Loan ID:</strong> ${loanId}</p>
            <p><strong>Customer ID:</strong> ${customerId}</p>
        `);
        $('#loanLiquidationModal').modal('show');
    }

    // Handle loan approval
    $('#confirmApproval').on('click', function() {
        var btn = $(this);
        var originalText = btn.html();

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Approving...');

        $.ajax({
            url: '<?= base_url('admin/wacs/acceptLoan') ?>/' + currentLoanId,
            method: 'PATCH',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#loanApprovalModal').modal('hide');
                    showNotification('success', response.message || 'Loan approved successfully!');
                    setTimeout(function() { location.reload(); }, 1500);
                } else {
                    // Check for specific "Invoice already generated" message
                    if (response.message && response.message.toLowerCase().includes('invoice already generated')) {
                        $('#loanApprovalModal').modal('hide');
                        showNotification('info', 'Approval has already been granted for this loan. The invoice has been generated.');
                        setTimeout(function() { location.reload(); }, 1500);
                    } else {
                        showNotification('error', response.message || 'Failed to approve loan.');
                    }
                }
            },
            error: function() {
                showNotification('error', 'An error occurred while approving the loan.');
            },
            complete: function() {
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Handle loan rejection
    $('#loanRejectionForm').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var btn = form.find('button[type="submit"]');
        var originalText = btn.html();

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Rejecting...');

        var formData = form.serialize();
        var loanId = $('#rejection_loan_id').val();

        $.ajax({
            url: '<?= base_url('admin/wacs/rejectLoan') ?>/' + loanId,
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#loanRejectionModal').modal('hide');
                    showNotification('success', response.message || 'Loan rejected successfully!');
                    setTimeout(function() { location.reload(); }, 1500);
                } else {
                    showNotification('error', response.message || 'Failed to reject loan.');
                }
            },
            error: function() {
                showNotification('error', 'An error occurred while rejecting the loan.');
            },
            complete: function() {
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Handle loan liquidation
    $('#loanLiquidationForm').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var btn = form.find('button[type="submit"]');
        var originalText = btn.html();

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Liquidating...');

        var formData = form.serialize();

        $.ajax({
            url: '<?= base_url('admin/wacs/liquidateLoan') ?>',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#loanLiquidationModal').modal('hide');
                    showNotification('success', response.message || 'Loan liquidated successfully!');
                    setTimeout(function() { location.reload(); }, 1500);
                } else {
                    showNotification('error', response.message || 'Failed to liquidate loan.');
                }
            },
            error: function() {
                showNotification('error', 'An error occurred while liquidating the loan.');
            },
            complete: function() {
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // API Status monitoring
    function refreshApiStatus() {
        $('#apiStatusContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Checking API status...</div>');

        $.ajax({
            url: '<?= base_url('admin/wacs/healthCheck') ?>',
            method: 'GET',
            dataType: 'json',
            timeout: 10000,
            success: function(status) {
                displayApiStatus(status);
            },
            error: function() {
                $('#apiStatusContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Error:</strong> Unable to check API status. Please check your connection.
                    </div>
                `);
            }
        });
    }

    function displayApiStatus(status) {
        var overallClass = status.overall_status === 'OK' ? 'success' : 'danger';
        var overallIcon = status.overall_status === 'OK' ? 'check-circle' : 'exclamation-triangle';

        var html = `
            <div class="row">
                <div class="col-md-3">
                    <div class="info-box bg-${overallClass}">
                        <span class="info-box-icon"><i class="fas fa-${overallIcon}"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Overall Status</span>
                            <span class="info-box-number">${status.overall_status}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box bg-${status.connectivity ? 'success' : 'danger'}">
                        <span class="info-box-icon"><i class="fas fa-${status.connectivity ? 'wifi' : 'wifi-slash'}"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Connectivity</span>
                            <span class="info-box-number">${status.connectivity ? 'OK' : 'FAIL'}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box bg-${status.authentication ? 'success' : 'warning'}">
                        <span class="info-box-icon"><i class="fas fa-${status.authentication ? 'key' : 'key-slash'}"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Authentication</span>
                            <span class="info-box-number">${status.authentication ? 'OK' : 'FAIL'}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box bg-info">
                        <span class="info-box-icon"><i class="fas fa-server"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Environment</span>
                            <span class="info-box-number">${status.environment}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        if (status.connectivity_error || status.auth_error) {
            html += `
                <div class="alert alert-warning mt-2">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Issues Detected:</strong><br>
                    ${status.connectivity_error ? 'Connectivity: ' + status.connectivity_error + '<br>' : ''}
                    ${status.auth_error ? 'Authentication: ' + status.auth_error : ''}
                </div>
            `;
        }

        html += `
            <small class="text-muted">
                <i class="fas fa-clock"></i> Last checked: ${status.timestamp} |
                <i class="fas fa-link"></i> ${status.base_url}
            </small>
        `;

        $('#apiStatusContent').html(html);
    }

    // Load API status on page load and set up auto-refresh
    refreshApiStatus();
    setInterval(refreshApiStatus, 300000); // Auto-refresh every 5 minutes
});

// Global function for refresh button
function refreshApiStatus() {
    $('#apiStatusContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Checking API status...</div>');

    $.ajax({
        url: '<?= base_url('admin/wacs/healthCheck') ?>',
        method: 'GET',
        dataType: 'json',
        timeout: 10000,
        success: function(status) {
            displayApiStatus(status);
        },
        error: function() {
            $('#apiStatusContent').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Error:</strong> Unable to check API status. Please check your connection.
                </div>
            `);
        }
    });
}

function displayApiStatus(status) {
    var overallClass = status.overall_status === 'OK' ? 'success' : 'danger';
    var overallIcon = status.overall_status === 'OK' ? 'check-circle' : 'exclamation-triangle';

    var html = `
        <div class="row">
            <div class="col-md-3">
                <div class="info-box bg-${overallClass}">
                    <span class="info-box-icon"><i class="fas fa-${overallIcon}"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Overall Status</span>
                        <span class="info-box-number">${status.overall_status}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box bg-${status.connectivity ? 'success' : 'danger'}">
                    <span class="info-box-icon"><i class="fas fa-${status.connectivity ? 'wifi' : 'wifi-slash'}"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Connectivity</span>
                        <span class="info-box-number">${status.connectivity ? 'OK' : 'FAIL'}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box bg-${status.authentication ? 'success' : 'warning'}">
                    <span class="info-box-icon"><i class="fas fa-${status.authentication ? 'key' : 'key-slash'}"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Authentication</span>
                        <span class="info-box-number">${status.authentication ? 'OK' : 'FAIL'}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-box bg-info">
                    <span class="info-box-icon"><i class="fas fa-server"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Environment</span>
                        <span class="info-box-number">${status.environment}</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    if (status.connectivity_error || status.auth_error) {
        html += `
            <div class="alert alert-warning mt-2">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Issues Detected:</strong><br>
                ${status.connectivity_error ? 'Connectivity: ' + status.connectivity_error + '<br>' : ''}
                ${status.auth_error ? 'Authentication: ' + status.auth_error : ''}
            </div>
        `;
    }

    html += `
        <small class="text-muted">
            <i class="fas fa-clock"></i> Last checked: ${status.timestamp} |
            <i class="fas fa-link"></i> ${status.base_url}
        </small>
    `;

    $('#apiStatusContent').html(html);
}
</script>
