<?php defined('BASEPATH') or exit('No direct script access allowed');
class Public_wacs extends MY_Controller {
    public function checkEligibilityForm() {
        $this->load->view('public/_header');
        $this->load->view('admin/wacs/check_eligibility');
        $this->load->view('public/_footer');
    }
    public function checkEligibility($ippis_number = null) {
        $this->load->model('admin/Onboarded_wacs_customers_model');
        if (!$ippis_number) {
            echo json_encode(['success' => false, 'message' => 'IPPIS number is required.']);
            return;
        }
        $baseUrl = $this->config->item('wacs_test_url');
        $token = null;
        // You may want to use a public token or skip auth for public endpoint
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/customer/' . urlencode($ippis_number) . '/loan-eligibility');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Accept: application/json',
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        $json = json_decode($response, true);
        $eligible = null;
        if (isset($json['success']) && $json['success'] && isset($json['data']['eligibility'])) {
            $eligible = $json['data']['eligibility'];

            try {
                $existing = $this->Onboarded_wacs_customers_model->get_by_ippis($ippis_number);
                if ($existing) {
                    // Update existing record
                    $this->Onboarded_wacs_customers_model->update_eligibility_by_ippis($ippis_number, $eligible);
                    log_message('info', 'Updated eligibility for IPPIS: ' . $ippis_number . ' to: ' . $eligible);
                } else {
                    // Insert new record with proper data structure
                    $insertData = [
                        'ippis_number' => $ippis_number,
                        'current_eligibility' => $eligible,
                        'eligibility' => $eligible,
                        'employee_status' => 'Active', // Default status
                        'created_at' => date('Y-m-d H:i:s')
                    ];

                    // Get table fields to ensure we only insert valid columns
                    $this->load->database();
                    $fields = $this->db->list_fields('onboarded_wacs_customers');
                    $validData = [];
                    foreach ($insertData as $field => $value) {
                        if (in_array($field, $fields)) {
                            $validData[$field] = $value;
                        }
                    }

                    if (!empty($validData)) {
                        $result = $this->Onboarded_wacs_customers_model->insert_onboarded_customer($validData);
                        log_message('info', 'Inserted new eligibility record for IPPIS: ' . $ippis_number . ' with ID: ' . $result);
                    }
                }
            } catch (Exception $e) {
                log_message('error', 'Error saving eligibility for IPPIS ' . $ippis_number . ': ' . $e->getMessage());
                // Don't break the response, just log the error
            }
        }
        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
        } else {
            echo $response;
        }
        curl_close($ch);
    }

    /**
     * Display public registration form
     */
    public function registrationForm() {
        $this->load->view('public_wacs_registration');
    }

    /**
     * Handle public customer registration
     * Endpoint: POST /public_wacs/register
     */
    public function register() {
        // Set JSON content type for proper response
        $this->output->set_content_type('application/json');

        // Get input data
        $ippis_number = $this->input->post('ippis_number', true);
        $bvn = $this->input->post('bvn', true);
        $account_number = $this->input->post('account_number', true);

        // Validate required fields and formats
        $errors = [];

        if (!$ippis_number) {
            $errors['ippis_number'] = 'IPPIS number is required.';
        } elseif (!preg_match('/^[0-9]+$/', $ippis_number)) {
            $errors['ippis_number'] = 'IPPIS number must contain only digits.';
        }

        if (!$bvn) {
            $errors['bvn'] = 'BVN is required.';
        } elseif (!preg_match('/^[0-9]{11}$/', $bvn)) {
            $errors['bvn'] = 'BVN must be exactly 11 digits.';
        }

        if (!$account_number) {
            $errors['account_number'] = 'Account number is required.';
        } elseif (!preg_match('/^[0-9]{10}$/', $account_number)) {
            $errors['account_number'] = 'Account number must be exactly 10 digits.';
        }

        if (!empty($errors)) {
            echo json_encode([
                'success' => false,
                'message' => 'Please correct the following errors:',
                'errors' => $errors,
                'csrf_hash' => $this->security->get_csrf_hash()
            ]);
            return;
        }

        // Load settings model to get WACS configuration
        $baseUrl = $this->settings->wacs_is_live == 0 ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        
        // Get authentication token
        $token = $this->authorizeWacsUser();
        if (!$token) {
            echo json_encode([
                'success' => false,
                'message' => 'Unable to authenticate with WACS system. Please try again later.',
                'csrf_hash' => $this->security->get_csrf_hash()
            ]);
            return;
        }

        // Prepare request data
        $requestData = [
            'ippis_number' => $ippis_number,
            'bvn' => $bvn,
            'account_number' => $account_number
        ];

        $json_body = json_encode($requestData);

        // Make API call to WACS (following same process as admin USSD onboarding)
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl . '/api/v1/lender/customers/register/ussd');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json',
            'Cache-Control: no-cache',
            'Pragma: no-cache'
        ));

        // Enhanced logging for debugging (same as admin controller)
        log_message('info', 'Public WACS Customer Registration API Call - IPPIS: ' . $ippis_number);
        log_message('info', 'Public WACS API URL: ' . $baseUrl . '/api/v1/lender/customers/register/ussd');
        log_message('info', 'Public WACS API Request Body: ' . $json_body);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        // Enhanced logging for debugging
        log_message('info', 'Public WACS API HTTP Code: ' . $httpCode);
        log_message('info', 'Public WACS API Response: ' . $response);

        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', 'Public WACS API Network Error: ' . $error);
            echo json_encode([
                'success' => false,
                'message' => 'Unable to connect to WACS system. Please try again later.',
                'csrf_hash' => $this->security->get_csrf_hash()
            ]);
            curl_close($ch);
            return;
        }

        curl_close($ch);

        $result = json_decode($response, true);

        // Log JSON decode errors
        if (json_last_error() !== JSON_ERROR_NONE) {
            log_message('error', 'Public WACS API JSON Decode Error: ' . json_last_error_msg());
            log_message('error', 'Public WACS API Raw Response: ' . $response);
        }

        // Follow same success criteria as admin controller (HTTP 201 or success flag)
        if ($httpCode == 201 || (isset($result['success']) && $result['success'])) {
            // Customer registered successfully via USSD

            // Check if customer data exists and insert into local database
            if (isset($result['data'])) {
                $this->load->model('admin/Onboarded_wacs_customers_model');

                // Check if customer already exists in local database
                $existing = $this->Onboarded_wacs_customers_model->get_by_ippis($ippis_number);

                if (!$existing) {
                    // Insert customer data into local database using same method as admin
                    $saved = $this->savePublicWacsCustomer($result['data']);
                } else {
                    $saved = true; // Already exists, consider as saved
                }

                if ($saved) {
                    echo json_encode([
                        'success' => true,
                        'message' => $result['message'] ?? 'Registration successful! You are now registered in the WACS system and eligible for loans.',
                        'data' => $result['data'] ?? null,
                        'csrf_hash' => $this->security->get_csrf_hash()
                    ]);
                } else {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Registration successful! However, there was an issue saving your details locally. Please contact administrator if you experience any issues.',
                        'csrf_hash' => $this->security->get_csrf_hash()
                    ]);
                }
            } else {
                // Success but no data returned
                echo json_encode([
                    'success' => true,
                    'message' => $result['message'] ?? 'Registration successful! You are now registered in the WACS system.',
                    'csrf_hash' => $this->security->get_csrf_hash()
                ]);
            }
        } else {
            // Enhanced error logging
            log_message('error', 'Public WACS Customer Registration Failed - HTTP Code: ' . $httpCode);
            log_message('error', 'Public WACS API Error Response: ' . json_encode($result));

            // Handle API errors (same as admin controller)
            $errorMsg = isset($result['message']) ? $result['message'] : 'Customer registration failed.';
            $errors = [];

            if (isset($result['errors']) && is_array($result['errors'])) {
                $formattedErrors = [];
                foreach ($result['errors'] as $field => $error) {
                    if (is_array($error)) {
                        $formattedErrors[] = is_numeric($field) ? implode('<br>', $error) : $field . ': ' . implode(', ', $error);
                        $errors[$field] = $error;
                    } else {
                        $formattedErrors[] = is_numeric($field) ? $error : $field . ': ' . $error;
                        $errors[$field] = [$error];
                    }
                }
                if (!empty($formattedErrors)) {
                    $errorMsg .= (!empty($errorMsg) ? '<br>' : '') . implode('<br>', $formattedErrors);
                }
            }

            // Add HTTP status code to error message for debugging
            if ($httpCode !== 200 && $httpCode !== 201) {
                $errorMsg .= ' (HTTP ' . $httpCode . ')';
            }

            echo json_encode([
                'success' => false,
                'message' => $errorMsg,
                'errors' => $errors,
                'csrf_hash' => $this->security->get_csrf_hash()
            ]);
        }
    }

    /**
     * Save public WACS customer data to local database
     */
    private function savePublicWacsCustomer($data) {
        try {
            $this->load->model('admin/Onboarded_wacs_customers_model');

            $customer = $data['customer'];
            $user = $customer['user'];
            $moreInfo = $data['more_info'];

            // Prepare data for insertion (same structure as admin registration)
            $insertData = [
                'customer_id' => $customer['id'],
                'mda' => $customer['mda'],
                'pfa_name' => $customer['pfaName'],
                'account_name' => $customer['accountName'],
                'account_number' => $customer['accountNumber'],
                'bank' => $customer['bank'],
                'bank_code' => $customer['bankCode'],
                'bvn' => $customer['bvn'],
                'gender' => $customer['gender'],
                'current_salary' => $customer['currentSalary'],
                'ippis_number' => $customer['ippisNumber'],
                'nationality' => $customer['nationality'],
                'address' => $customer['address'],
                'state' => $customer['state'],
                'employee_status' => $customer['employeeStatus'],

                // User information
                'user_id' => $user['id'],
                'user_first_name' => $user['firstName'],
                'user_last_name' => $user['lastName'],
                'user_middle_name' => $user['middleName'],
                'user_phone_number' => $user['phoneNumber'],
                'user_email' => $user['email'],
                'user_role' => $user['role'],

                // More info (truncated for brevity - same as admin version)
                'more_info_id' => $moreInfo['id'],
                'data_source' => $moreInfo['data_source'],
                'ippis_number' => $moreInfo['ippis_number'],
                'first_name' => $moreInfo['first_name'],
                'surname' => $moreInfo['surname'],
                'full_name' => $moreInfo['full_name'],
                'eligibility' => $data['eligibility'],
                'response_json' => json_encode($data)
            ];

            // Check if customer already exists
            $existing = $this->Onboarded_wacs_customers_model->get_by_ippis($customer['ippisNumber']);

            if ($existing) {
                // Update existing record
                return $this->Onboarded_wacs_customers_model->update_by_ippis($customer['ippisNumber'], $insertData);
            } else {
                // Insert new record
                return $this->Onboarded_wacs_customers_model->insert_onboarded_customer($insertData);
            }

        } catch (Exception $e) {
            log_message('error', 'Error saving public WACS customer: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Authorize WACS user and get authentication token
     * Similar to admin/Wacs controller method
     */
    private function authorizeWacsUser() {
        try {
            // Load settings model to get WACS configuration
            $this->load->model('admin/Setting_model');
            $settings = $this->setting_model->get_general_settings();
            
            $baseUrl = $this->settings->wacs_is_live == 0 ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
            $username = $this->settings->wacs_is_live == 0 ? $this->settings->wacs_test_username : $this->settings->wacs_live_username;
            $password = $this->settings->wacs_is_live == 0 ? $this->settings->wacs_test_password : $this->settings->wacs_live_password;
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/login');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(array(
                'username' => $username,
                'password' => $password
            )));
            
            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                $error = 'Network Error: ' . curl_error($ch);
                log_message('error', $error);
                curl_close($ch);
                return null;
            }
            curl_close($ch);
            
            $response = json_decode($response);
            if (isset($response->data->token)) {
                return $response->data->token;
            } else {
                $error = 'WACS Auth Error: ' . json_encode($response);
                log_message('error', $error);
                return null;
            }
        } catch (Exception $e) {
            log_message('error', 'Exception in authorizeWacsUser: ' . $e->getMessage());
            return null;
        }
    }
}



