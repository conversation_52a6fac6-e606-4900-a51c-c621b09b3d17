<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><i class="fas fa-user-plus"></i> WACS Customer Registration</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('admin') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('admin/wacs') ?>">WACS</a></li>
                        <li class="breadcrumb-item active">Register Customer</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-user-check"></i> Customer Registration via USSD
                            </h3>
                        </div>
                        <div class="card-body">
                            
                            <form id="createWacsCustomerForm" novalidate>
                                <?php echo form_hidden($this->security->get_csrf_token_name(), $this->security->get_csrf_hash()); ?>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="ippis_number">
                                                <i class="fas fa-id-card text-primary"></i> IPPIS Number <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                                </div>
                                                <input type="text" class="form-control form-control-lg" id="ippis_number" name="ippis_number"
                                                       required pattern="[0-9]+" placeholder="Enter IPPIS number">
                                                <div class="invalid-feedback"></div>
                                            </div>
                                            <small class="form-text text-muted">Customer's unique IPPIS identification number</small>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="bvn">
                                                <i class="fas fa-fingerprint text-success"></i> BVN <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fas fa-shield-alt"></i></span>
                                                </div>
                                                <input type="text" class="form-control form-control-lg" id="bvn" name="bvn"
                                                       required pattern="[0-9]{11}" maxlength="11" placeholder="Enter 11-digit BVN">
                                                <div class="invalid-feedback"></div>
                                            </div>
                                            <small class="form-text text-muted">11-digit Bank Verification Number</small>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="account_number">
                                                <i class="fas fa-university text-info"></i> Account Number <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fas fa-credit-card"></i></span>
                                                </div>
                                                <input type="text" class="form-control form-control-lg" id="account_number" name="account_number"
                                                       required pattern="[0-9]{10}" maxlength="10" placeholder="Enter 10-digit account number">
                                                <div class="invalid-feedback"></div>
                                            </div>
                                            <small class="form-text text-muted">10-digit bank account number</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-4">
                                    <div class="col-md-12">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <a href="<?= base_url('admin/wacs') ?>" class="btn btn-secondary btn-lg">
                                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                                            </a>
                                            <button type="submit" class="btn btn-primary btn-lg">
                                                <i class="fas fa-user-plus"></i> Register Customer
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>

                            <div id="registrationResult" class="mt-4" style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    // Form validation and submission
    $('#createWacsCustomerForm').on('submit', function(e) {
        e.preventDefault();

        // Clear previous validation
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').text('');
        $('#registrationResult').hide();

        var form = $(this);
        var submitBtn = form.find('button[type="submit"]');
        var originalText = submitBtn.html();

        // Validate form
        if (!validateForm()) {
            return false;
        }

        // Show loading state
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Registering Customer...');

        // Prepare data
        var formData = {
            ippis_number: $('#ippis_number').val().trim(),
            bvn: $('#bvn').val().trim(),
            account_number: $('#account_number').val().trim()
        };

        // Add CSRF token
        formData[$('input[name="<?= $this->security->get_csrf_token_name() ?>"]').attr('name')] =
                 $('input[name="<?= $this->security->get_csrf_token_name() ?>"]').val();

        $.ajax({
            url: '<?= base_url('admin/wacs/createWacsCustomer') ?>',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showResult('success', response.message || 'Customer registered successfully!', response.data);
                    form[0].reset();
                } else {
                    if (response.errors && typeof response.errors === 'object') {
                        // Handle field-specific validation errors
                        Object.keys(response.errors).forEach(function(field) {
                            var input = $('#' + field);
                            if (input.length) {
                                input.addClass('is-invalid');
                                input.siblings('.invalid-feedback').text(response.errors[field]);
                            }
                        });
                    }
                    showResult('error', response.message || 'Registration failed. Please try again.');
                }
            },
            error: function(xhr) {
                var errorMsg = 'An error occurred during registration.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                showResult('error', errorMsg);
            },
            complete: function() {
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Form validation
    function validateForm() {
        var isValid = true;

        // IPPIS validation
        var ippis = $('#ippis_number').val().trim();
        if (!ippis || !/^[0-9]+$/.test(ippis)) {
            showFieldError('ippis_number', 'Please enter a valid IPPIS number (numbers only)');
            isValid = false;
        }

        // BVN validation
        var bvn = $('#bvn').val().trim();
        if (!bvn || !/^[0-9]{11}$/.test(bvn)) {
            showFieldError('bvn', 'Please enter a valid 11-digit BVN');
            isValid = false;
        }

        // Account number validation
        var accountNumber = $('#account_number').val().trim();
        if (!accountNumber || !/^[0-9]{10}$/.test(accountNumber)) {
            showFieldError('account_number', 'Please enter a valid 10-digit account number');
            isValid = false;
        }

        return isValid;
    }

    // Show field error
    function showFieldError(fieldId, message) {
        var field = $('#' + fieldId);
        field.addClass('is-invalid');
        field.siblings('.invalid-feedback').text(message);
    }

    // Show result
    function showResult(type, message, data = null) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';

        var html = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="${icon}"></i> <strong>${message}</strong>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;

        if (type === 'success' && data) {
            html += `
                <div class="card card-success">
                    <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-user-check"></i> Registration Successful</h3>
                    </div>
                    <div class="card-body">
                        <p><strong>Customer has been successfully registered in the WACS system.</strong></p>
                        <div class="row">
                            <div class="col-md-6">
                                <a href="<?= base_url('admin/wacs/createLoanForm') ?>" class="btn btn-primary btn-block">
                                    <i class="fas fa-plus"></i> Create Loan for Customer
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="<?= base_url('admin/wacs/listOnboardedCustomers') ?>" class="btn btn-info btn-block">
                                    <i class="fas fa-list"></i> View All Customers
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        $('#registrationResult').html(html).show();

        // Scroll to result
        $('html, body').animate({
            scrollTop: $('#registrationResult').offset().top - 100
        }, 500);
    }

    // Input formatting
    $('#ippis_number, #bvn, #account_number').on('input', function() {
        // Remove non-numeric characters
        this.value = this.value.replace(/[^0-9]/g, '');

        // Remove validation errors when user starts typing
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').text('');
    });

    // Auto-focus first input
    $('#ippis_number').focus();
});
</script>
