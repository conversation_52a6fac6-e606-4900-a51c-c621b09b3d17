<!-- Content Wrapper. Contains page content -->
<div
	class="content-wrapper">
	<!-- Content Header (Page header) -->
	<div class="content-header">
		<div class="container-fluid">
			<div class="row mb-2">
				<div class="col-sm-6">
					<h1 class="m-0">
						<i class="fas fa-user-shield text-primary"></i>
						Welcome, <span class="text-primary"><?php echo $this->session->userdata('username') ?></span>
					</h1>
					<p class="text-muted mb-0">Administrator Dashboard - Manage system operations</p>
				</div>
				<!-- /.col -->
				<div class="col-sm-6">
					<ol class="breadcrumb float-sm-right">
						<li class="breadcrumb-item">
							<a href="#" class="text-primary"><?= trans('home') ?></a>
						</li>
						<li class="breadcrumb-item active">Admin Dashboard</li>
					</ol>
				</div>
				<!-- /.col -->
			</div>
			<!-- /.row -->
		</div>
		<!-- /.container-fluid -->
	</div>
	<!-- /.content-header -->

	<!-- Main content -->
	<section class="content">
		<div
			class="container-fluid">
			<!-- Enhanced Info boxes -->
			<div class="row mb-4">
				<div class="col-lg-3 col-md-6">
					<div class="small-box bg-gradient-success">
						<div class="inner">
							<h3 id="wallet_balance"><?php echo formatMoney(wallet_balance()); ?></h3>
							<p>Wallet Balance</p>
						</div>
						<div class="icon">
							<i class="fas fa-wallet"></i>
						</div>
						<div class="small-box-footer p-2">
							<div class="row">
								<div class="col-6">
									<a href="<?php echo base_url('admin/transactions/fund_wallet') ?>" class="btn btn-success btn-sm btn-block" data-toggle="ajax-modal">
										<i class="fas fa-plus"></i> Fund
									</a>
								</div>
								<div class="col-6">
									<a href="<?php echo base_url('admin/transactions') ?>" class="btn btn-outline-light btn-sm btn-block">
										<i class="fas fa-list"></i> Statement
									</a>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="col-lg-3 col-md-6">
					<div class="small-box bg-gradient-warning">
						<div class="inner">
							<h3><?php echo countwhere('customer_successful_search') ?></h3>
							<p>Total Searches</p>
						</div>
						<div class="icon">
							<i class="fas fa-search"></i>
						</div>
						<a href="<?= base_url('admin/customers/search_history') ?>" class="small-box-footer">
							More info <i class="fas fa-arrow-circle-right"></i>
						</a>
					</div>
				</div>

				<div class="col-lg-3 col-md-6">
					<div class="small-box bg-gradient-info">
						<div class="inner">
							<h3><?php echo countwhere('customers', ['onremita' => 1]) ?></h3>
							<p>Remita DRF Customers</p>
						</div>
						<div class="icon">
							<i class="fas fa-users"></i>
						</div>
						<a href="<?= base_url('admin/customers') ?>" class="small-box-footer">
							More info <i class="fas fa-arrow-circle-right"></i>
						</a>
					</div>
				</div>

				<div class="col-lg-3 col-md-6">
					<div class="small-box bg-gradient-danger">
						<div class="inner">
							<h3><?php echo formatMoney(countwhere('customer_successful_search') * 100) ?></h3>
							<p>Search Costs</p>
						</div>
						<div class="icon">
							<i class="fas fa-money-bill"></i>
						</div>
						<a href="<?= base_url('admin/reports/search_costs') ?>" class="small-box-footer">
							More info <i class="fas fa-arrow-circle-right"></i>
						</a>
					</div>
				</div>
			</div>
			<!-- /.row -->


			<!-- Quick Actions -->
			<div class="row mb-4">
				<div class="col-12">
					<div class="card card-primary card-outline">
						<div class="card-header">
							<h3 class="card-title">
								<i class="fas fa-bolt"></i> Quick Actions
							</h3>
						</div>
						<div class="card-body">
							<div class="row">
								<div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
									<a href="<?= base_url('admin/customers/add') ?>" class="btn btn-app bg-primary">
										<i class="fas fa-user-plus"></i> Add Customer
									</a>
								</div>
								<div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
									<a href="<?= base_url('admin/customers/search') ?>" class="btn btn-app bg-success">
										<i class="fas fa-search"></i> Search Customer
									</a>
								</div>
								<div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
									<a href="<?= base_url('admin/wacs') ?>" class="btn btn-app bg-warning">
										<i class="fas fa-hand-holding-usd"></i> WACS Portal
									</a>
								</div>
								<div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
									<a href="<?= base_url('admin/transactions') ?>" class="btn btn-app bg-info">
										<i class="fas fa-exchange-alt"></i> Transactions
									</a>
								</div>
								<div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
									<a href="<?= base_url('admin/reports') ?>" class="btn btn-app bg-secondary">
										<i class="fas fa-chart-bar"></i> Reports
									</a>
								</div>
								<div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
									<a href="<?= base_url('admin/settings') ?>" class="btn btn-app bg-dark">
										<i class="fas fa-cog"></i> Settings
									</a>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Main row -->
			<div class="row">
				<!-- Recent Customers -->
				<div class="col-lg-8">
					<div class="card card-primary card-outline">
						<div class="card-header">
							<h3 class="card-title">
								<i class="fas fa-users"></i> Recent Customers
							</h3>
							<div class="card-tools">
								<a href="<?= base_url('admin/customers') ?>" class="btn btn-sm btn-primary">
									<i class="fas fa-eye"></i> View All
								</a>
							</div>
						</div>
						<div class="card-body p-0">
							<div class="table-responsive">
								<table class="table table-striped">
									<thead>
										<tr>
											<th>Customer</th>
											<th>Organization</th>
											<th>Salary</th>
											<th>Status</th>
											<th>Searches</th>
											<th>Last Checked</th>
											<th>Actions</th>
										</tr>
									</thead>
									<tbody>
										<?php foreach ($customers as $customer): ?>
											<?php
											$no_of_searchs = countwhere('customer_successful_search', ['customerid' => $customer->remitacustomerid]);
											$salary = !empty(getby(['phone' => $customer->phone], 'customer_salary_loan')) ? getby(['phone' => $customer->phone], 'customer_salary_loan') : null;
											$sal = !empty($salary) ? json_decode($salary->salary) : null;
											?>
											<tr>
												<td>
													<div class="user-panel d-flex">
														<div class="image">
															<div class="img-circle elevation-2 bg-primary d-flex align-items-center justify-content-center" style="width: 35px; height: 35px; color: white; font-size: 14px;">
																<?= strtoupper(substr($customer->fullname, 0, 2)) ?>
															</div>
														</div>
														<div class="info ml-2">
															<a href="<?php echo base_url('admin/customers/salary_loan_details/' . $customer->phone . '?cid=' . $customer->remitacustomerid) ?>" class="d-block font-weight-bold">
																<?php echo $customer->fullname ?>
															</a>
															<small class="text-muted"><?= $customer->phone ?></small>
														</div>
													</div>
												</td>
												<td>
													<span class="text-sm"><?php echo $customer->organization ?></span>
												</td>
												<td>
													<?php if (!empty($sal)): ?>
														<span class="badge badge-success">
															<?php echo formatMoney($sal[0]->amount) ?>
														</span>
													<?php else: ?>
														<span class="badge badge-secondary">No data</span>
													<?php endif ?>
												</td>
												<td>
													<?php if ($customer->onremita == 1): ?>
														<span class="badge badge-success">
															<i class="fas fa-check"></i> On Remita
														</span>
													<?php else: ?>
														<span class="badge badge-danger">
															<i class="fas fa-times"></i> Not on Remita
														</span>
													<?php endif ?>
												</td>
												<td>
													<span class="badge badge-warning">
														<?php echo $no_of_searchs ?>
														<i class="fas fa-search"></i>
													</span>
												</td>
												<td>
													<small class="text-muted">
														<?php echo formatDate($customer->lastchecked); ?>
													</small>
												</td>
												<td>
													<a href="<?php echo base_url('admin/customers/salary_loan_details/' . $customer->phone . '?cid=' . $customer->remitacustomerid) ?>" class="btn btn-sm btn-primary">
														<i class="fas fa-eye"></i> Details
													</a>
												</td>
											</tr>
										<?php endforeach ?>
									</tbody>
								</table>
							</div>
						</div>
						<div class="card-footer text-center">
							<a href="<?= base_url('admin/customers') ?>" class="btn btn-outline-primary">
								<i class="fas fa-users"></i> View All Customers
							</a>
						</div>
					</div>
				</div>


				<!-- TABLE: LATEST ORDERS -->

					<!-- /.card -->
				</div>
				<!-- /.col -->

				<!-- Sidebar -->
				<div class="col-lg-4">
					<!-- System Status -->
					<div class="card card-success card-outline mb-3">
						<div class="card-header">
							<h3 class="card-title">
								<i class="fas fa-server"></i> System Status
							</h3>
						</div>
						<div class="card-body">
							<div class="info-box">
								<span class="info-box-icon bg-success">
									<i class="fas fa-database"></i>
								</span>
								<div class="info-box-content">
									<span class="info-box-text">Database</span>
									<span class="info-box-number">Connected</span>
								</div>
							</div>

							<div class="info-box">
								<span class="info-box-icon bg-info">
									<i class="fas fa-cloud"></i>
								</span>
								<div class="info-box-content">
									<span class="info-box-text">Remita API</span>
									<span class="info-box-number">Online</span>
								</div>
							</div>

							<div class="info-box">
								<span class="info-box-icon bg-warning">
									<i class="fas fa-clock"></i>
								</span>
								<div class="info-box-content">
									<span class="info-box-text">Last Sync</span>
									<span class="info-box-number" id="lastSync">--</span>
								</div>
							</div>
						</div>
					</div>

					<!-- Recent Salary Searches -->
					<div class="card card-info card-outline">
						<div class="card-header">
							<h3 class="card-title">
								<i class="fas fa-search"></i> Recent Salary Searches
							</h3>
						</div>
						<div class="card-body p-0">
							<?php $this->load->view('admin/customers/recentsalarysearch') ?>
						</div>
					</div>

					<!-- Quick Stats -->
					<div class="card card-warning card-outline">
						<div class="card-header">
							<h3 class="card-title">
								<i class="fas fa-chart-pie"></i> Quick Stats
							</h3>
						</div>
						<div class="card-body">
							<div class="row">
								<div class="col-6">
									<div class="description-block border-right">
										<span class="description-percentage text-success">
											<i class="fas fa-caret-up"></i> 17%
										</span>
										<h5 class="description-header"><?= countwhere('customers') ?></h5>
										<span class="description-text">TOTAL CUSTOMERS</span>
									</div>
								</div>
								<div class="col-6">
									<div class="description-block">
										<span class="description-percentage text-warning">
											<i class="fas fa-caret-left"></i> 0%
										</span>
										<h5 class="description-header"><?= countwhere('customer_successful_search') ?></h5>
										<span class="description-text">TOTAL SEARCHES</span>
									</div>
								</div>
							</div>

							<div class="row mt-3">
								<div class="col-6">
									<div class="description-block border-right">
										<span class="description-percentage text-info">
											<i class="fas fa-caret-up"></i> 20%
										</span>
										<h5 class="description-header"><?= countwhere('customers', ['onremita' => 1]) ?></h5>
										<span class="description-text">ON REMITA</span>
									</div>
								</div>
								<div class="col-6">
									<div class="description-block">
										<span class="description-percentage text-danger">
											<i class="fas fa-caret-down"></i> 18%
										</span>
										<h5 class="description-header"><?= countwhere('customers', ['onremita' => 0]) ?></h5>
										<span class="description-text">NOT ON REMITA</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div
					class="col-md-4 pt-5">

					<?php $loans = getalldata('loans', null, 'id', 'DESC', 0, 4); ?>
					<div class="card">
						<div class="card-header">
							<h3 class="card-title">Recent Loans</h3>

							<div class="card-tools">
								<button type="button" class="btn btn-tool" data-widget="collapse">
									<i class="fa fa-minus"></i>
								</button>
								<button type="button" class="btn btn-tool" data-widget="remove">
									<i class="fa fa-times"></i>
								</button>
							</div>
						</div>
						<!-- /.card-header -->
						<div class="card-body p-0">
							<ul class="products-list product-list-in-card pl-2 pr-2">
								<?php foreach ($loans as $loan): ?>
									<?php
									$customer_successful_search = empty(getby(['customerid' => $loan->customerid], 'customer_successful_search')) ? false : getby(['customerid' => $loan->customerid], 'customer_successful_search');

									$customerData = !empty($customer_successful_search->response) ? json_decode($customer_successful_search->response)->data : null;

									?>

										<li class="item"> <div class="product-info">
											<a
												href="#">
												<?php echo !empty($customerData->customerName) ? ucwords(strtolower($customerData->customerName)) : '<span ="text-danger">no name</span>' ?>
												<span class="badge badge-info float-right"><?php echo $loan->bookedwith ?></span>
											</a>
											<br>


											<small>
												<span class="d-block">
													<b>Man.  Ref:</b>
													<?php echo empty($loan->mandateref) ? 'none' : $loan->mandateref . ' <span class="fa fa-check-circle text-success"></span>' ?>
												</span>

											</small>
											<small>
												<b>Amount:</b>
												<?php echo formatMoney($loan->loanamount) ?>
											</small>
											|
											<small>
												<b>
													Repayment:</b>
												<?php echo formatMoney($loan->collectionamount) ?>
											</small>
											|
											<small>
												<b>Rate:</b>
												<?php echo round($loan->rate, 2) . '%' ?>
											</small>
											<br>
											<small>
												<b>Created on:</b>
												<?php echo formatDate($loan->datecreated) ?>
											</small>
											<a href="<?php echo base_url('admin/customers/salary_loan_details?cid=' . $customerData->customerId) ?>" class="btn btn-primary btn-sm float-right">
												Details
											</a>


										</div>
									</li>
								<?php endforeach ?>

								<!-- /.item -->


							</ul>
						</div>
						<!-- /.card-body -->
						<div class="card-footer text-center">
							<a href="<?php echo base_url('admin/customers/loans') ?>" class="btn btn-secondary btn-sm">
								<i class="fa fa-upload"></i>
								All Loans</a>

						</div>
						<!-- /.card-footer -->
					</div>


				</div>
				<!-- /.col -->
			</div>
			<!-- /.row -->
		</div>
		<!--/. container-fluid -->
	</section>
	<!-- /.content -->
</div>
<!-- /.content-wrapper -->

<script>
$(document).ready(function() {
	// Update last sync time
	updateLastSyncTime();

	// Refresh sync time every minute
	setInterval(updateLastSyncTime, 60000);

	// Add hover effects to small boxes
	$('.small-box').hover(
		function() {
			$(this).addClass('elevation-3');
		},
		function() {
			$(this).removeClass('elevation-3');
		}
	);

	// Add click animation to quick action buttons
	$('.btn-app').on('click', function() {
		$(this).addClass('pulse');
		setTimeout(() => {
			$(this).removeClass('pulse');
		}, 300);
	});

	// Add hover effects to table rows
	$('.table tbody tr').hover(
		function() {
			$(this).addClass('table-active');
		},
		function() {
			$(this).removeClass('table-active');
		}
	);

	// Add hover effects to info boxes
	$('.info-box').hover(
		function() {
			$(this).addClass('elevation-2');
		},
		function() {
			$(this).removeClass('elevation-2');
		}
	);

	function updateLastSyncTime() {
		var now = new Date();
		var timeString = now.toLocaleTimeString();
		$('#lastSync').text(timeString);
	}

	// Animate numbers on page load
	animateNumbers();

	function animateNumbers() {
		$('.description-header').each(function() {
			var $this = $(this);
			var countTo = parseInt($this.text());

			if (!isNaN(countTo)) {
				$({ countNum: 0 }).animate({
					countNum: countTo
				}, {
					duration: 2000,
					easing: 'linear',
					step: function() {
						$this.text(Math.floor(this.countNum));
					},
					complete: function() {
						$this.text(this.countNum);
					}
				});
			}
		});
	}
});
</script>

<style>
.small-box {
	transition: all 0.3s ease;
}

.small-box:hover {
	transform: translateY(-2px);
}

.btn-app {
	transition: all 0.3s ease;
}

.btn-app:hover {
	transform: scale(1.05);
}

.pulse {
	animation: pulse 0.3s ease;
}

@keyframes pulse {
	0% { transform: scale(1); }
	50% { transform: scale(1.1); }
	100% { transform: scale(1); }
}

.info-box {
	transition: all 0.3s ease;
}

.info-box:hover {
	transform: translateX(5px);
}

.table tbody tr {
	transition: all 0.3s ease;
}

.table tbody tr:hover {
	background-color: #f8f9fa !important;
	transform: translateX(5px);
}

.user-panel .image {
	transition: all 0.3s ease;
}

.user-panel:hover .image {
	transform: scale(1.1);
}

.description-block {
	transition: all 0.3s ease;
}

.description-block:hover {
	transform: scale(1.05);
}

.card {
	transition: all 0.3s ease;
}

.card:hover {
	box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}
</style>


<!-- PAGE PLUGINS -->
<!-- SparkLine --><script src="<?= base_url() ?>assets/plugins/sparkline/jquery.sparkline.min.js"> </script>
<!-- jVectorMap -->
<script src="<?= base_url() ?>assets/plugins/jvectormap/jquery-jvectormap-1.2.2.min.js"></script>
<script src="<?= base_url() ?>assets/plugins/jvectormap/jquery-jvectormap-world-mill-en.js"></script>
<!-- SlimScroll 1.3.0 -->
<script src="<?= base_url() ?>assets/plugins/slimScroll/jquery.slimscroll.min.js"></script>
<!-- ChartJS 1.0.2 -->
<script src="<?= base_url() ?>assets/plugins/chartjs-old/Chart.min.js"></script>

<!-- PAGE SCRIPTS -->

