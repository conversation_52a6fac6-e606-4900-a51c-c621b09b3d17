<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><i class="fas fa-stethoscope"></i> WACS API Diagnostics</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('admin') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('admin/wacs') ?>">WACS</a></li>
                        <li class="breadcrumb-item active">API Diagnostics</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-tools"></i> WACS API Connection Test
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Purpose:</strong> This tool helps diagnose WACS API connection issues and test various endpoints.
                            </div>

                            <!-- API Configuration Display -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fas fa-cog"></i> Current Configuration</h5>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-sm">
                                                <tr>
                                                    <td><strong>Environment:</strong></td>
                                                    <td>
                                                        <span class="badge badge-<?= $this->settings->wacs_is_live ? 'danger' : 'warning' ?>">
                                                            <?= $this->settings->wacs_is_live ? 'LIVE' : 'TEST' ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Base URL:</strong></td>
                                                    <td>
                                                        <code><?= $this->settings->wacs_is_live ? $this->settings->wacs_live_url : $this->settings->wacs_test_url ?></code>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Username:</strong></td>
                                                    <td>
                                                        <code><?= $this->settings->wacs_is_live ? $this->settings->wacs_live_username : $this->settings->wacs_test_username ?></code>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Password:</strong></td>
                                                    <td>
                                                        <code><?= str_repeat('*', strlen($this->settings->wacs_is_live ? $this->settings->wacs_live_password : $this->settings->wacs_test_password)) ?></code>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fas fa-list"></i> Test Options</h5>
                                        </div>
                                        <div class="card-body">
                                            <button type="button" class="btn btn-primary btn-block mb-2" onclick="testConnection()">
                                                <i class="fas fa-wifi"></i> Test Basic Connection
                                            </button>
                                            <button type="button" class="btn btn-success btn-block mb-2" onclick="testAuthentication()">
                                                <i class="fas fa-key"></i> Test Authentication
                                            </button>
                                            <button type="button" class="btn btn-info btn-block mb-2" onclick="testCustomerRegistration()">
                                                <i class="fas fa-user-plus"></i> Test Customer Registration
                                            </button>
                                            <button type="button" class="btn btn-warning btn-block mb-2" onclick="testEligibilityCheck()">
                                                <i class="fas fa-check-circle"></i> Test Eligibility Check
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Test Results -->
                            <div id="testResults" style="display: none;">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-clipboard-list"></i> Test Results</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="testContent">
                                            <!-- Results will be loaded here -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Test Customer Registration Form -->
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fas fa-user-plus"></i> Test Customer Registration</h5>
                                        </div>
                                        <div class="card-body">
                                            <form id="testRegistrationForm">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="test_ippis">IPPIS Number</label>
                                                            <input type="text" class="form-control" id="test_ippis" placeholder="Enter test IPPIS">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="test_bvn">BVN</label>
                                                            <input type="text" class="form-control" id="test_bvn" placeholder="Enter test BVN">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="test_account">Account Number</label>
                                                            <input type="text" class="form-control" id="test_account" placeholder="Enter test account">
                                                        </div>
                                                    </div>
                                                </div>
                                                <button type="button" class="btn btn-primary" onclick="testSpecificRegistration()">
                                                    <i class="fas fa-play"></i> Test Registration
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    // Auto-focus on page load
    $('#test_ippis').focus();
});

function showResults(title, content, type = 'info') {
    var alertClass = type === 'success' ? 'alert-success' : (type === 'error' ? 'alert-danger' : 'alert-info');
    var icon = type === 'success' ? 'fa-check-circle' : (type === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle');
    
    var html = `
        <div class="alert ${alertClass}">
            <h5><i class="fas ${icon}"></i> ${title}</h5>
            <div style="white-space: pre-wrap; font-family: monospace; background: rgba(0,0,0,0.1); padding: 10px; border-radius: 4px; margin-top: 10px;">
${content}
            </div>
        </div>
    `;
    
    $('#testContent').html(html);
    $('#testResults').show();
    
    // Scroll to results
    $('html, body').animate({
        scrollTop: $('#testResults').offset().top - 100
    }, 500);
}

function testConnection() {
    showResults('Testing Connection...', 'Checking basic connectivity to WACS API...', 'info');
    
    $.ajax({
        url: '<?= base_url('admin/wacs/testApiConnection') ?>',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showResults('Connection Test - SUCCESS', response.message + '\n\nDetails:\n' + JSON.stringify(response.data, null, 2), 'success');
            } else {
                showResults('Connection Test - FAILED', response.message, 'error');
            }
        },
        error: function(xhr) {
            var errorMsg = 'Connection test failed';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMsg = xhr.responseJSON.message;
            }
            showResults('Connection Test - ERROR', errorMsg, 'error');
        }
    });
}

function testAuthentication() {
    showResults('Testing Authentication...', 'Attempting to authenticate with WACS API...', 'info');
    
    $.ajax({
        url: '<?= base_url('admin/wacs/testApiAuth') ?>',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showResults('Authentication Test - SUCCESS', response.message + '\n\nToken: ' + (response.token ? response.token.substring(0, 50) + '...' : 'N/A'), 'success');
            } else {
                showResults('Authentication Test - FAILED', response.message, 'error');
            }
        },
        error: function(xhr) {
            var errorMsg = 'Authentication test failed';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMsg = xhr.responseJSON.message;
            }
            showResults('Authentication Test - ERROR', errorMsg, 'error');
        }
    });
}

function testCustomerRegistration() {
    showResults('Testing Customer Registration...', 'Testing customer registration endpoint with sample data...', 'info');
    
    $.ajax({
        url: '<?= base_url('admin/wacs/testCustomerRegistration') ?>',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showResults('Customer Registration Test - SUCCESS', response.message + '\n\nResponse:\n' + JSON.stringify(response.data, null, 2), 'success');
            } else {
                showResults('Customer Registration Test - FAILED', response.message + '\n\nDetails:\n' + JSON.stringify(response.errors || {}, null, 2), 'error');
            }
        },
        error: function(xhr) {
            var errorMsg = 'Customer registration test failed';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMsg = xhr.responseJSON.message;
            }
            showResults('Customer Registration Test - ERROR', errorMsg, 'error');
        }
    });
}

function testEligibilityCheck() {
    showResults('Testing Eligibility Check...', 'Testing eligibility check endpoint...', 'info');
    
    $.ajax({
        url: '<?= base_url('admin/wacs/testEligibilityCheck') ?>',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showResults('Eligibility Check Test - SUCCESS', response.message + '\n\nResponse:\n' + JSON.stringify(response.data, null, 2), 'success');
            } else {
                showResults('Eligibility Check Test - FAILED', response.message, 'error');
            }
        },
        error: function(xhr) {
            var errorMsg = 'Eligibility check test failed';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMsg = xhr.responseJSON.message;
            }
            showResults('Eligibility Check Test - ERROR', errorMsg, 'error');
        }
    });
}

function testSpecificRegistration() {
    var ippis = $('#test_ippis').val().trim();
    var bvn = $('#test_bvn').val().trim();
    var account = $('#test_account').val().trim();
    
    if (!ippis || !bvn || !account) {
        showResults('Test Registration - ERROR', 'Please fill in all fields (IPPIS, BVN, Account Number)', 'error');
        return;
    }
    
    showResults('Testing Specific Registration...', 'Testing registration with provided data:\nIPPIS: ' + ippis + '\nBVN: ' + bvn + '\nAccount: ' + account, 'info');
    
    $.ajax({
        url: '<?= base_url('admin/wacs/createWacsCustomer') ?>',
        method: 'POST',
        data: {
            ippis_number: ippis,
            bvn: bvn,
            account_number: account,
            '<?= $this->security->get_csrf_token_name() ?>': '<?= $this->security->get_csrf_hash() ?>'
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showResults('Specific Registration Test - SUCCESS', response.message + '\n\nResponse:\n' + JSON.stringify(response.data, null, 2), 'success');
            } else {
                showResults('Specific Registration Test - FAILED', response.message + '\n\nErrors:\n' + JSON.stringify(response.errors || {}, null, 2), 'error');
            }
        },
        error: function(xhr) {
            var errorMsg = 'Specific registration test failed';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMsg = xhr.responseJSON.message;
            }
            showResults('Specific Registration Test - ERROR', errorMsg + '\n\nFull Response:\n' + JSON.stringify(xhr.responseJSON, null, 2), 'error');
        }
    });
}
</script>
