<!-- Edit Loan Product Modal -->
<div class="modal fade" id="editLoanProductModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Edit Loan Product</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="editLoanProductForm">
                <div class="modal-body">
                    <input type="hidden" id="edit_product_id" name="product_id">
                    
                    <div class="form-group">
                        <label for="edit_product_name">Product Name</label>
                        <input type="text" class="form-control" id="edit_product_name" name="product_name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_interest_rate">Interest Rate (%)</label>
                        <input type="number" step="0.01" class="form-control" id="edit_interest_rate" name="interest_rate" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_min_amount">Minimum Amount</label>
                        <input type="number" class="form-control" id="edit_min_amount" name="min_amount" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_max_amount">Maximum Amount</label>
                        <input type="number" class="form-control" id="edit_max_amount" name="max_amount" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_min_tenor">Minimum Tenor (months)</label>
                        <input type="number" class="form-control" id="edit_min_tenor" name="min_tenor" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_max_tenor">Maximum Tenor (months)</label>
                        <input type="number" class="form-control" id="edit_max_tenor" name="max_tenor" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_status">Status</label>
                        <select class="form-control" id="edit_status" name="status" required>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Product</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Edit loan product function
function editLoanProduct(productId) {
    // Get product details via AJAX
    $.ajax({
        url: '<?= base_url('admin/wacs/getLoanProduct/') ?>' + productId,
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var product = response.product;
                $('#edit_product_id').val(product.id);
                $('#edit_product_name').val(product.product_name);
                $('#edit_interest_rate').val(product.interest_rate);
                $('#edit_min_amount').val(product.min_amount);
                $('#edit_max_amount').val(product.max_amount);
                $('#edit_min_tenor').val(product.min_tenor);
                $('#edit_max_tenor').val(product.max_tenor);
                $('#edit_status').val(product.status);
                
                $('#editLoanProductModal').modal('show');
            } else {
                $.notify(response.message, 'error');
            }
        },
        error: function() {
            $.notify('Error loading product details', 'error');
        }
    });
}

// Handle edit form submission
$('#editLoanProductForm').on('submit', function(e) {
    e.preventDefault();
    
    var formData = $(this).serialize();
    var btn = $(this).find('button[type="submit"]');
    
    btn.prop('disabled', true).text('Updating...');
    
    $.ajax({
        url: '<?= base_url('admin/wacs/updateLoanProduct') ?>',
        method: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $.notify(response.message, 'success');
                $('#editLoanProductModal').modal('hide');
                // Reload the page or update the table
                location.reload();
            } else {
                $.notify(response.message, 'error');
            }
        },
        error: function() {
            $.notify('Error updating product', 'error');
        },
        complete: function() {
            btn.prop('disabled', false).text('Update Product');
        }
    });
});
</script>