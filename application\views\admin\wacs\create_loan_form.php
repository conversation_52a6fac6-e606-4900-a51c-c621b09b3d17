<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Create Loan Application</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('admin/wacs') ?>">WACS</a></li>
                        <li class="breadcrumb-item active">Create Loan</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-12">
                    <!-- Progress Steps -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="progress-steps">
                                <div class="step active" id="step-indicator-1">
                                    <div class="step-number">1</div>
                                    <div class="step-title">Customer Registration</div>
                                </div>
                                <div class="step" id="step-indicator-2">
                                    <div class="step-number">2</div>
                                    <div class="step-title">Loan Application</div>
                                </div>
                                <div class="step" id="step-indicator-3">
                                    <div class="step-number">3</div>
                                    <div class="step-title">Review & Submit</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Customer Registration -->
                    <div class="card" id="step-1">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-user-plus"></i> Step 1: Customer Registration
                            </h3>
                        </div>
                        <div class="card-body">
                            <form id="customerRegistrationForm" novalidate>
                                <?php echo form_hidden($this->security->get_csrf_token_name(), $this->security->get_csrf_hash()); ?>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="ippis_number">IPPIS Number <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="ippis_number" name="ippis_number" 
                                                   required maxlength="20" pattern="[0-9]+" 
                                                   value="<?php echo isset($_GET['ippis_number']) ? htmlspecialchars($_GET['ippis_number']) : ''; ?>">
                                            <div class="invalid-feedback"></div>
                                            <small class="form-text text-muted">Enter the customer's IPPIS number</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="bvn">BVN <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="bvn" name="bvn"
                                                   required maxlength="11" pattern="[0-9]{11}"
                                                   value="<?php echo isset($_GET['bvn']) ? htmlspecialchars($_GET['bvn']) : ''; ?>">
                                            <div class="invalid-feedback"></div>
                                            <small class="form-text text-muted">11-digit Bank Verification Number</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="account_number">Account Number <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="account_number" name="account_number"
                                                   required maxlength="10" pattern="[0-9]{10}"
                                                   value="<?php echo isset($_GET['account_number']) ? htmlspecialchars($_GET['account_number']) : ''; ?>">
                                            <div class="invalid-feedback"></div>
                                            <small class="form-text text-muted">10-digit bank account number</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- <div class="row mt-4">
                                    <div class="col-md-12">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i>
                                            <strong>Note:</strong> This step will register the customer in the WACS system using their IPPIS, BVN, and account number.
                                        </div>
                                    </div>
                                </div> -->

                                <div class="row mt-3">
                                    <div class="col-md-12 text-right">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-arrow-right"></i> Next Step
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Step 2: Loan Application (Hidden initially) -->
                    <div class="card" id="step-2" style="display: none;">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-money-bill-wave"></i> Step 2: Loan Application
                            </h3>
                        </div>
                        <div class="card-body">
                           

                            <form id="loanApplicationForm" novalidate>
                                <?php echo form_hidden($this->security->get_csrf_token_name(), $this->security->get_csrf_hash()); ?>
                                <input type="hidden" id="customer_id" name="customer_id">
                                
                                <div class="row">
                                    <div class="col-md-8">
										<div class="row">
											<div class="col-md-6">
												<div class="form-group">
													<label for="loan_product_id">Loan Product <span class="text-danger">*</span></label>
													<select class="form-control" id="loan_product_id" name="loan_product_id" required>
														<option value="">Select Loan Product</option>
													</select>
													<div class="invalid-feedback"></div>
												</div>
											</div>
											<div class="col-md-6">
												<div class="form-group">
													<label for="loan_amount">Loan Amount <span class="text-danger">*</span></label>
													<div class="input-group">
														<div class="input-group-prepend">
															<span class="input-group-text">₦</span>
														</div>
														<input type="number" class="form-control" id="loan_amount" name="amount" 
															required min="1000" step="0.01">
														<div class="invalid-feedback"></div>
													</div>
													<div id="eligibilityWarning" class="alert alert-warning mt-2" style="display: none;"></div>
												</div>
											</div>
											<div class="col-md-12">
												<div id="loanCalculation" class="" style="display: none;">
														<!-- Loan calculation details will be shown here -->
													</div>
												</div>
											</div>
									</div>
									<div class="col-md-4">
										<div id="customerInfo" class="mb-4" style="display: none;">
											<!-- Customer information will be populated here -->
										</div>
									</div>
                                </div>

                            
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-secondary btn-lg" id="backToStep1">
                                            <i class="fas fa-arrow-left"></i> Back
                                        </button>
                                    </div>
                                    <div class="col-md-6 text-right">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-arrow-right"></i> Review Application
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Step 3: Review & Submit (Hidden initially) -->
                    <div class="card" id="step-3" style="display: none;">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-check-circle"></i> Step 3: Review & Submit
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="applicationReview">
                                <!-- Application review details will be populated here -->
                            </div>

                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-secondary btn-lg" id="backToStep2">
                                        <i class="fas fa-arrow-left"></i> Back
                                    </button>
                                </div>
                                <div class="col-md-6 text-right">
                                    <button type="button" class="btn btn-success btn-lg" id="submitApplication">
                                        <i class="fas fa-paper-plane"></i> Submit Application
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 60%;
    width: 80%;
    height: 2px;
    background-color: #dee2e6;
    z-index: 1;
}

.step.active:not(:last-child)::after,
.step.completed:not(:last-child)::after {
    background-color: #007bff;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
}

.step.active .step-number {
    background-color: #007bff;
    color: white;
}

.step.completed .step-number {
    background-color: #28a745;
    color: white;
}

.step-title {
    font-size: 14px;
    text-align: center;
    color: #6c757d;
}

.step.active .step-title {
    color: #007bff;
    font-weight: 600;
}

.step.completed .step-title {
    color: #28a745;
    font-weight: 600;
}
</style>

<script>
$(document).ready(function() {
    // CSRF token setup
    var csrfName = '<?= $this->security->get_csrf_token_name(); ?>';
    var csrfHash = '<?= $this->security->get_csrf_hash(); ?>';

    // Global variables
    var customerData = null;
    var loanProducts = [];
    var productsMap = {};
    var currentStep = 1;

    // Utility functions
    function formatCurrency(amount) {
        return parseFloat(amount).toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2});
    }

    function showStep(stepNumber) {
        // Hide all steps
        $('#step-1, #step-2, #step-3').hide();

        // Show current step
        $('#step-' + stepNumber).show();

        // Update step indicators
        $('.step').removeClass('active completed');
        for (let i = 1; i < stepNumber; i++) {
            $('#step-indicator-' + i).addClass('completed');
        }
        $('#step-indicator-' + stepNumber).addClass('active');

        currentStep = stepNumber;
    }

    function showError(container, message) {
        container.html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ' + message + '</div>');
    }

    function showSuccess(container, message) {
        container.html('<div class="alert alert-success"><i class="fas fa-check-circle"></i> ' + message + '</div>');
    }

    function clearValidation(form) {
        form.find('.is-invalid').removeClass('is-invalid');
        form.find('.invalid-feedback').text('');
    }

    function validateForm(form) {
        var isValid = true;
        clearValidation(form);

        form.find('[required]').each(function() {
            var field = $(this);
            var value = field.val().trim();

            if (!value) {
                field.addClass('is-invalid');
                field.siblings('.invalid-feedback').text('This field is required.');
                isValid = false;
            } else {
                // Additional validation based on field type
                var pattern = field.attr('pattern');
                if (pattern && !new RegExp(pattern).test(value)) {
                    field.addClass('is-invalid');
                    field.siblings('.invalid-feedback').text('Please enter a valid format.');
                    isValid = false;
                }
            }
        });

        return isValid;
    }

    // Load loan products
    function loadLoanProducts() {
        return $.get('<?= base_url('admin/wacs/getLoanProducts') ?>', function(response) {
            if (response.success && Array.isArray(response.data)) {
                loanProducts = response.data.filter(function(p) {
                    return p.status === 'active' || p.status === 1 || p.is_active === 1;
                });

                productsMap = {};
                loanProducts.forEach(function(p) {
                    productsMap[p.id] = p;
                });

                // Populate loan product dropdown
                var options = loanProducts.map(function(p) {
                    var rate = p.interest_rate ? p.interest_rate + '%' : '';
                    var period = p.payback_period ? p.payback_period : (p.tenure ? p.tenure : '');
                    var label = `${p.title} - ${rate} - ${period}`;
                    return `<option value='${p.id}'>${label}</option>`;
                }).join('');

                $('#loan_product_id').html('<option value="">Select Loan Product</option>' + options);
            }
        }, 'json').fail(function() {
            console.warn('Failed to load loan products');
        });
    }

    // Step 1: Customer Registration Form Handler
    $('#customerRegistrationForm').on('submit', function(e) {
        e.preventDefault();

        if (!validateForm($(this))) {
            return;
        }

        var form = $(this);
        var btn = form.find('button[type="submit"]');
        var originalText = btn.html();

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Registering...');

        var formData = {
            ippis_number: $('#ippis_number').val().trim(),
            bvn: $('#bvn').val().trim(),
            account_number: $('#account_number').val().trim()
        };

        // Add CSRF token
        formData[csrfName] = csrfHash;

        $.ajax({
            url: '<?= base_url('admin/wacs/registerCustomerUssd') ?>',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success && response.data && response.data.customer) {
                    customerData = response.data;

                    // Display customer information
                    displayCustomerInfo(response.data);

                    // Move to step 2
                    showStep(2);
                } else {

                    // Check if customer already exists (multiple possible messages)
                    var message = response.message || '';
                    var isExistingCustomer = message.toLowerCase().includes('creditor has already created this customer') ||
                                           message.toLowerCase().includes('already enrolled') ||
                                           message.toLowerCase().includes('customer already exists') ||
                                           message.toLowerCase().includes('user has already enrolled');

                    if (isExistingCustomer) {
                        // First try to get customer from local database
                        var ippis = $('#ippis_number').val().trim();
                        getExistingCustomerFromLocal(ippis);
                    } else {
                        // Handle field-specific validation errors
                        if (response.errors && typeof response.errors === 'object') {
                            Object.keys(response.errors).forEach(function(field) {
                                var errorMsg = response.errors[field];
                                var input = form.find('[name="' + field + '"]');
                                if (input.length) {
                                    input.addClass('is-invalid');
                                    input.siblings('.invalid-feedback').text(errorMsg);
                                }
                            });

                            // Show general error message if provided
                            if (response.message) {
                                form.prepend('<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                                    '<i class="fas fa-exclamation-triangle"></i> ' + response.message +
                                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                                    '<span aria-hidden="true">&times;</span></button></div>');
                            }
                        } else {
                            var errorMsg = response.message || 'Customer registration failed. Please try again.';
                            form.prepend('<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                                '<i class="fas fa-exclamation-triangle"></i> ' + errorMsg +
                                '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                                '<span aria-hidden="true">&times;</span></button></div>');
                        }
                    }
                }
            },
            error: function(xhr) {
                var errorMsg = 'An error occurred during registration. Please try again.';
                var response = xhr.responseJSON;
				console.log(response);
                if (response) {
                    if (response.errors && typeof response.errors === 'object') {
                        // Handle field-specific errors from server error response
                        Object.keys(response.errors).forEach(function(field) {
                            var errorMsg = response.errors[field];
                            var input = form.find('[name="' + field + '"]');
                            if (input.length) {
                                input.addClass('is-invalid');
                                input.siblings('.invalid-feedback').text(errorMsg);
                            }
                        });

                        if (response.message) {
                            errorMsg = response.message;
                        }
                    } else if (response.message) {
                        errorMsg = response.message;
                    }
                }

                form.prepend('<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                    '<i class="fas fa-exclamation-triangle"></i> ' + errorMsg +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span></button></div>');
            },
            complete: function() {
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Display customer information
    function displayCustomerInfo(data) {
        var customer = data.customer;
        var user = customer.user;

        // Get eligibility from loan eligibility endpoint
        getCustomerEligibility(customer.ippisNumber, function(eligibility) {
            displayCustomerInfoWithEligibility(data, eligibility);
        });
    }

    // Get customer eligibility from the proper endpoint
    function getCustomerEligibility(ippisNumber, callback) {
        $.ajax({
            url: '<?= base_url('admin/wacs/checkEligibility') ?>/' + encodeURIComponent(ippisNumber),
            method: 'GET',
            dataType: 'json',
            success: function(res) {
                var eligibility = 0;
                if (res && res.success) {
                    if (res.eligibility) {
                        eligibility = parseFloat(res.eligibility) || 0;
                    } else if (res.data && res.data.eligibility) {
                        eligibility = parseFloat(res.data.eligibility) || 0;
                    }
                }
                callback(eligibility);
            },
            error: function(xhr) {
                console.warn('Failed to get customer eligibility:', xhr);
                callback(0); // Default to 0 if failed
            }
        });
    }

    // Display customer information with eligibility
    function displayCustomerInfoWithEligibility(data, eligibility) {
        var customer = data.customer;
        var user = customer.user;

        var html = `
           <div class="row mb-3">
				<div class="col-12">
					<ul class="list-group list-group-flush border rounded">
						<li class="list-group-item d-flex align-items-center py-2">
							<i class="fas fa-user text-primary mr-3" style="width: 20px;"></i>
							<div>
								<small class="text-muted d-block">Customer Name</small>
								<strong>${user.firstName} ${user.lastName}</strong>
							</div>
						</li>
						<li class="list-group-item d-flex align-items-center py-2">
							<i class="fas fa-university text-success mr-3" style="width: 20px;"></i>
							<div>
								<small class="text-muted d-block">Account Number</small>
								<strong>${customer.accountNumber}</strong>
								<small class="text-muted d-block">${customer.bank}</small>
							</div>
						</li>
						<li class="list-group-item d-flex align-items-center py-2">
							<i class="fas fa-id-card text-info mr-3" style="width: 20px;"></i>
							<div>
								<small class="text-muted d-block">IPPIS Number</small>
								<strong>${customer.ippisNumber}</strong>
							</div>
						</li>
						<li class="list-group-item d-flex align-items-center py-2">
							<i class="fas fa-building text-warning mr-3" style="width: 20px;"></i>
							<div>
								<small class="text-muted d-block">Ministry/Agency</small>
								<strong style="font-size: 13px; line-height: 1.3;">${customer.mda}</strong>
							</div>
						</li>
						<li class="list-group-item d-flex align-items-center py-2">
							<i class="fas fa-money-bill-wave text-success mr-3" style="width: 20px;"></i>
							<div>
								<small class="text-muted d-block">Loan Eligibility</small>
								<strong class="text-success">${formatCurrency(eligibility)}</strong>
								<small class="text-muted d-block">Maximum loan amount</small>
							</div>
						</li>
						<li class="list-group-item d-flex align-items-center py-2">
							<i class="fas fa-wallet text-info mr-3" style="width: 20px;"></i>
							<div>
								<small class="text-muted d-block">Current Salary</small>
								<strong class="text-info">${formatCurrency(customer.currentSalary)}</strong>
								<small class="text-muted d-block">Monthly income</small>
							</div>
						</li>
					</ul>
				</div>
			</div>
        `;

        $('#customerInfo').html(html).show();
        $('#customer_id').val(customer.id);

        // Set eligibility for validation
        $('#loan_amount').attr('max', eligibility);
        window.customerEligibility = eligibility;
    }

    // Loan amount validation
    $('#loan_amount').on('input', function() {
        var amount = parseFloat($(this).val()) || 0;
        var eligibility = window.customerEligibility || 0;
        var warningDiv = $('#eligibilityWarning');

        if (amount > eligibility) {
            warningDiv.html(`<i class="fas fa-exclamation-triangle"></i> Loan amount exceeds eligibility of ₦${formatCurrency(eligibility)}`).show();
        } else {
            warningDiv.hide();
        }

        updateLoanCalculation();
    });

    // Loan product change handler
    $('#loan_product_id').on('change', function() {
        updateLoanCalculation();
    });

    // Update loan calculation
    function updateLoanCalculation() {
        var productId = $('#loan_product_id').val();
        var amount = parseFloat($('#loan_amount').val()) || 0;
        var calculationDiv = $('#loanCalculation');

        if (productsMap[productId] && amount > 0) {
            var product = productsMap[productId];
            var rate = parseFloat(product.interest_rate) || 0;
            var period = parseInt(product.payback_period || product.tenure) || 1;
            var totalRepayable = amount + (amount * rate * period / 100);
            var monthlyRepayment = totalRepayable / period;

            var html = `
                <div class="card card-info">
                    <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-calculator"></i> Loan Calculation</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled mb-0">
                                    <li class="d-flex align-items-center mb-2 p-2 border-left border-primary bg-light">
                                        <i class="fas fa-money-bill text-primary mr-3" style="width: 20px;"></i>
                                        <div>
                                            <small class="text-muted d-block">Principal Amount</small>
                                            <strong class="text-primary">₦${formatCurrency(amount)}</strong>
                                        </div>
                                    </li>
                                    <li class="d-flex align-items-center mb-2 p-2 border-left border-warning bg-light">
                                        <i class="fas fa-chart-line text-warning mr-3" style="width: 20px;"></i>
                                        <div>
                                            <small class="text-muted d-block">Total Repayable</small>
                                            <strong class="text-warning">₦${formatCurrency(totalRepayable)}</strong>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled mb-0">
                                    <li class="d-flex align-items-center mb-2 p-2 border-left border-success bg-light">
                                        <i class="fas fa-calendar-alt text-success mr-3" style="width: 20px;"></i>
                                        <div>
                                            <small class="text-muted d-block">Monthly Payment</small>
                                            <strong class="text-success">₦${formatCurrency(monthlyRepayment)}</strong>
                                        </div>
                                    </li>
                                    <li class="d-flex align-items-center mb-2 p-2 border-left border-info bg-light">
                                        <i class="fas fa-percentage text-info mr-3" style="width: 20px;"></i>
                                        <div>
                                            <small class="text-muted d-block">Interest Rate</small>
                                            <strong class="text-info">${rate}% per annum</strong>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="alert alert-info border-0 bg-light">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-info-circle text-info mr-2"></i>
                                        <div>
                                            <strong>Loan Product:</strong> ${product.name || product.title || 'N/A'} <br>
                                            <small class="text-muted">Tenure: ${period} month(s)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            calculationDiv.html(html).show();
        } else {
            calculationDiv.hide();
        }
    }

    // Step 2: Loan Application Form Handler
    $('#loanApplicationForm').on('submit', function(e) {
        e.preventDefault();

        if (!validateForm($(this))) {
            return;
        }

        var amount = parseFloat($('#loan_amount').val()) || 0;
        var eligibility = window.customerEligibility || 0;

        if (amount > eligibility) {
            $('#loan_amount').addClass('is-invalid');
            $('#loan_amount').siblings('.invalid-feedback').text('Loan amount exceeds customer eligibility.');
            return;
        }

        // Prepare review data and move to step 3
        prepareApplicationReview();
        showStep(3);
    });

    // Prepare application review
    function prepareApplicationReview() {
        var customer = customerData.customer;
        var user = customer.user;
        var productId = $('#loan_product_id').val();
        var product = productsMap[productId];
        var amount = parseFloat($('#loan_amount').val());
        var rate = parseFloat(product.interest_rate) || 0;
        var period = parseInt(product.payback_period || product.tenure) || 1;
        var totalRepayable = amount + (amount * rate * period / 100);
        var monthlyRepayment = totalRepayable / period;

        var html = `
            <div class="row">
                <div class="col-md-12">
                    <h5><i class="fas fa-file-alt"></i> Application Summary</h5>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Customer Details</h6>
                        </div>
                        <div class="card-body">
                            <strong>Name:</strong> ${user.firstName} ${user.lastName}<br>
                            <strong>IPPIS Number:</strong> ${customer.ippisNumber}<br>
                            <strong>Account Number:</strong> ${customer.accountNumber}<br>
                            <strong>Bank:</strong> ${customer.bank}<br>
                            <strong>MDA:</strong> ${customer.mda}<br>
                            <strong>Current Salary:</strong> ₦${formatCurrency(customer.currentSalary)}
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Loan Details</h6>
                        </div>
                        <div class="card-body">
                            <strong>Product:</strong> ${product.name || product.title || 'N/A'}<br>
                            <strong>Principal Amount:</strong> ₦${formatCurrency(amount)}<br>
                            <strong>Interest Rate:</strong> ${rate}%<br>
                            <strong>Tenure:</strong> ${period} month(s)<br>
                            <strong>Monthly Repayment:</strong> ₦${formatCurrency(monthlyRepayment)}<br>
                            <strong>Total Repayable:</strong> ₦${formatCurrency(totalRepayable)}
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Important:</strong> Please review all details carefully before submitting.
                    </div>
                </div>
            </div>
        `;

        $('#applicationReview').html(html);
    }

    // Navigation handlers
    $('#backToStep1').on('click', function() {
        showStep(1);
    });

    $('#backToStep2').on('click', function() {
        showStep(2);
    });

    // Final submission handler
    $('#submitApplication').on('click', function() {
        var btn = $(this);
        var originalText = btn.html();

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Submitting...');

        var applicationData = {
            customer_id: $('#customer_id').val(),
            loan_product_id: $('#loan_product_id').val(),
            amount: $('#loan_amount').val()
        };
        applicationData[csrfName] = csrfHash;

        $.ajax({
            url: '<?= base_url('admin/wacs/applyLoan') ?>',
            method: 'POST',
            data: applicationData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    var successMsg = response.message || 'Loan application submitted successfully!';
                    $('#step-3 .card-body').html(`
                        <div class="alert alert-success text-center">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <h4>Application Submitted Successfully!</h4>
                            <p>${successMsg}</p>
                            <p class="mb-0">
                                <a href="<?= base_url('admin/wacs') ?>" class="btn btn-primary">
                                    <i class="fas fa-home"></i> Back to Dashboard
                                </a>
                                <a href="<?= base_url('admin/wacs/createLoanForm') ?>" class="btn btn-outline-primary ml-2">
                                    <i class="fas fa-plus"></i> Create Another Loan
                                </a>
                            </p>
                        </div>
                    `);

                    // Mark step 3 as completed
                    $('#step-indicator-3').removeClass('active').addClass('completed');
                } else {
                    var errorMsg = response.message || 'Loan application failed. Please try again.';
                    $('#applicationReview').prepend(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> ${errorMsg}
                        </div>
                    `);
                }
            },
            error: function(xhr) {
                var errorMsg = 'An error occurred while submitting the application. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                $('#applicationReview').prepend(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> ${errorMsg}
                    </div>
                `);
            },
            complete: function() {
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Function to get existing customer from local database first
    function getExistingCustomerFromLocal(ippis) {
        var form = $('#customerRegistrationForm');
        var btn = form.find('button[type="submit"]');

        // Show loading state
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Checking local database...');

        $.ajax({
            url: '<?= base_url('admin/wacs/getLocalCustomerByIppis') ?>/' + encodeURIComponent(ippis),
            method: 'GET',
            dataType: 'json',
            success: function(response) {

                if (response.success && response.customer) {
                    // Customer found in local database
                    var customer = response.customer;

                    // Prepare customer data for step 2
                    customerData = {
                        customer: {
                            id: customer.customer_id,
                            mda: customer.mda,
                            pfaName: customer.pfa_name,
                            accountName: customer.account_name,
                            accountNumber: customer.account_number,
                            bank: customer.bank,
                            bankCode: customer.bank_code,
                            bvn: customer.bvn,
                            gender: customer.gender,
                            currentSalary: customer.current_salary,
                            ippisNumber: customer.ippis_number,
                            nationality: customer.nationality,
                            address: customer.address,
                            state: customer.state,
                            employeeStatus: customer.employee_status,
                            user: {
                                id: customer.user_id,
                                firstName: customer.user_first_name,
                                lastName: customer.user_last_name,
                                middleName: customer.user_middle_name,
                                phoneNumber: customer.user_phone_number,
                                email: customer.user_email,
                                role: customer.user_role
                            }
                        }
                    };

                    // Display customer information
                    displayCustomerInfo(customerData);

                    // Show success message about existing customer
                    form.prepend('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                        '<i class="fas fa-check-circle"></i> Customer found! Proceeding to loan application...' +
                        '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                        '<span aria-hidden="true">&times;</span></button></div>');

                    // Move to step 2
                    setTimeout(function() {
                        showStep(2);
                    }, 2000);
                } else {
                    // Customer not found in local database, try WACS API
                    getExistingCustomerDetails(ippis);
                }
            },
            error: function(xhr) {
                console.error('Error getting local customer:', xhr);
                // If local lookup fails, try WACS API
                getExistingCustomerDetails(ippis);
            },
            complete: function() {
                // Reset button state will be handled by the next function call
            }
        });
    }

    // Function to get existing customer details from WACS API
    function getExistingCustomerDetails(ippis) {
        var form = $('#customerRegistrationForm');
        var btn = form.find('button[type="submit"]');

        // Show loading state
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Retrieving customer details...');

        $.ajax({
            url: '<?= base_url('admin/wacs/getCustomerOnboardingStatus') ?>/' + encodeURIComponent(ippis),
            method: 'GET',
            dataType: 'json',
            success: function(response) {

                if (response.success && response.data && response.data.customer) {
                    // Customer exists, prepare data for step 2
                    customerData = response.data;

                    // Display customer information
                    displayCustomerInfo(response.data);

                    // Show success message about existing customer
                    form.prepend('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                        '<i class="fas fa-check-circle"></i> Customer found in WACS system! Proceeding to loan application.' +
                        '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                        '<span aria-hidden="true">&times;</span></button></div>');

                    // Move to step 2
                    setTimeout(function() {
                        showStep(2);
                    }, 2000);
                } else {
                    // Customer not found or no data returned
                    var message = response.message || 'Customer exists but details could not be retrieved. Please contact administrator.';
                    form.prepend('<div class="alert alert-warning alert-dismissible fade show" role="alert">' +
                        '<i class="fas fa-exclamation-triangle"></i> ' + message +
                        '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                        '<span aria-hidden="true">&times;</span></button></div>');
                }
            },
            error: function(xhr) {
                console.error('Error getting customer details:', xhr);
                var errorMsg = 'Error retrieving customer details. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                form.prepend('<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                    '<i class="fas fa-exclamation-triangle"></i> ' + errorMsg +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span></button></div>');
            },
            complete: function() {
                // Reset button state
                btn.prop('disabled', false).html('<i class="fas fa-arrow-right"></i> Next Step');
            }
        });
    }

    // Clear validation errors when user starts typing
    $('#customerRegistrationForm input').on('input', function() {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').text('');
        // Remove alert messages when user starts correcting
        $('#customerRegistrationForm .alert-danger').fadeOut();
    });

    // Initialize
    loadLoanProducts();

    // Auto-focus first input
    $('#ippis_number').focus();
});
</script>



