<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Check Customer Onboarding Status</h1>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Enter IPPIS Number</h3>
                        </div>
                        <div class="card-body">
                            <form id="checkStatusForm">
                                <div class="form-group">
                                    <label for="ippis_number">IPPIS Number</label>
                                    <input type="text" class="form-control" id="ippis_number" name="ippis_number" required>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Check Status
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div id="statusResult" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Customer Status</h3>
                            </div>
                            <div class="card-body">
                                <div id="statusContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    $('#checkStatusForm').on('submit', function(e) {
        e.preventDefault();
        
        var ippis = $('#ippis_number').val().trim();
        if (!ippis) {
            $.notify('Please enter IPPIS number', 'error');
            return;
        }

        var btn = $(this).find('button[type="submit"]');
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Checking...');

        $.ajax({
            url: '<?= base_url('admin/wacs/getCustomerOnboardingStatus/') ?>' + encodeURIComponent(ippis),
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success && response.data && response.data.customer) {
                    displayCustomerStatus(response.data.customer, response.message);
                } else {
                    $('#statusContent').html(`
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> ${response.message || 'Customer not found or not onboarded'}
                        </div>
                    `);
                    $('#statusResult').show();
                }
            },
            error: function(xhr) {
                var errorMsg = 'Error checking status';
                try {
                    var response = JSON.parse(xhr.responseText);
                    errorMsg = response.message || errorMsg;
                } catch(e) {}
                
                $('#statusContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-times"></i> ${errorMsg}
                    </div>
                `);
                $('#statusResult').show();
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-search"></i> Check Status');
            }
        });
    });

    function displayCustomerStatus(customer, message) {
        var eligibility = customer.eligibility || 0;
        var statusBadge = customer.employeeStatus === 'Active' ? 
            '<span class="badge badge-success">Active</span>' : 
            '<span class="badge badge-warning">' + (customer.employeeStatus || 'Unknown') + '</span>';

        var html = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> ${message}
            </div>
            
            <ul class="list-group list-group-flush">
                <li class="list-group-item d-flex justify-content-between">
                    <strong>Customer Name:</strong>
                    <span>${customer.user.firstName} ${customer.user.middleName || ''} ${customer.user.lastName}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between">
                    <strong>IPPIS Number:</strong>
                    <span>${customer.ippisNumber}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between">
                    <strong>Account Number:</strong>
                    <span>${customer.accountNumber || 'N/A'}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between">
                    <strong>Bank:</strong>
                    <span>${customer.bank || 'N/A'}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between">
                    <strong>Ministry/Agency:</strong>
                    <span>${customer.mda || 'N/A'}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between">
                    <strong>Employee Status:</strong>
                    <span>${statusBadge}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between">
                    <strong>Current Salary:</strong>
                    <span>₦${formatCurrency(customer.currentSalary || 0)}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between">
                    <strong>Loan Eligibility:</strong>
                    <span class="text-success"><strong>₦${formatCurrency(eligibility)}</strong></span>
                </li>
                <li class="list-group-item d-flex justify-content-between">
                    <strong>Email:</strong>
                    <span>${customer.user.email || 'N/A'}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between">
                    <strong>Phone:</strong>
                    <span>${customer.user.phoneNumber || 'N/A'}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between">
                    <strong>State:</strong>
                    <span>${customer.state || 'N/A'}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between">
                    <strong>PFA:</strong>
                    <span>${customer.pfaName || 'N/A'}</span>
                </li>
            </ul>

            <div class="mt-3">
                <a href="<?= base_url('admin/wacs/createLoanForm?ippis_number=') ?>${customer.ippisNumber}" 
                   class="btn btn-success">
                    <i class="fas fa-plus"></i> Create Loan
                </a>
            </div>
        `;

        $('#statusContent').html(html);
        $('#statusResult').show();
    }

    function formatCurrency(amount) {
        return parseFloat(amount || 0).toLocaleString('en-NG', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
});
</script>